from rest_framework import permissions, serializers
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .serializers import SaveGameSerializer
from ..models import SaveGame
from ..logic.xp import create_default_save_data

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    """Serializer for user information"""
    class Meta:
        model = User
        fields = ['id', 'email', 'username', 'is_staff', 'is_active']
        read_only_fields = ['id', 'email', 'username', 'is_staff', 'is_active']

class ProfileSaveView(APIView):
    """GET/POST autosave lub wybrany slot."""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        slot = int(request.query_params.get("slot", 0))
        data = SaveGame.load_for(request.user, slot)
        if data is None:
            return Response({"detail": "no save"}, status=204)
        return Response(SaveGameSerializer(data, context={"request": request}).data)

    def post(self, request):
        ser = SaveGameSerializer(data=request.data, context={"request": request})
        ser.is_valid(raise_exception=True)
        ser.save()
        return Response({"detail": "saved"}, status=200)


class SaveSlotListView(APIView):
    """GET – lista zapisów (slot 0-3), POST – nadpisz wybrany slot (1-3)."""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        saves = SaveGame.objects.filter(user=request.user).order_by("slot")
        ser = SaveGameSerializer(
            saves, many=True, context={"request": request}
        )
        return Response(ser.data)

    def post(self, request):
        import sys
        print(f"💾 SaveSlotListView POST called by user: {request.user}", file=sys.stderr)
        print(f"💾 Request data: {request.data}", file=sys.stderr)

        # oczekujemy: {"slot": 1, "data": {...}}
        ser = SaveGameSerializer(data=request.data, context={"request": request})
        print(f"💾 Serializer created", file=sys.stderr)

        try:
            ser.is_valid(raise_exception=True)
            print(f"💾 Serializer is valid: {ser.validated_data}", file=sys.stderr)
        except Exception as e:
            print(f"❌ Serializer validation error: {e}", file=sys.stderr)
            raise

        if ser.validated_data["slot"] == 0:
            return Response({"detail": "slot 0 is autosave"}, status=400)

        try:
            result = ser.save()
            print(f"💾 Save successful, result: {result}", file=sys.stderr)
            return Response({"detail": "saved"}, status=200)
        except Exception as e:
            print(f"❌ Save error: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            raise

    def delete(self, request):
        # oczekujemy: {"slot": 1} w query params lub body
        slot = request.query_params.get("slot") or request.data.get("slot")
        if not slot:
            return Response({"detail": "slot parameter required"}, status=400)

        try:
            slot = int(slot)
        except ValueError:
            return Response({"detail": "slot must be integer"}, status=400)

        if slot == 0:
            return Response({"detail": "cannot delete autosave"}, status=400)
        if slot < 1 or slot > 3:
            return Response({"detail": "slot must be 1-3"}, status=400)

        try:
            save_game = SaveGame.objects.get(user=request.user, slot=slot)
            save_game.delete()
            return Response({"detail": "save deleted"}, status=200)
        except SaveGame.DoesNotExist:
            return Response({"detail": "save not found"}, status=404)


class NewGameView(APIView):
    """
    POST /api/v1/profile/newgame/
    ─────────────────────────────
    Reset player's save game to default starting values.
    This creates a fresh save in slot 0 with:
    - All attributes at level 1, 0 XP
    - All subskills at level 0, 0 XP
    - No chosen perks
    - Starting scene: first scene from first subchapter of first chapter
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        # Create fresh save data
        default_data = create_default_save_data()

        # Save to slot 0 (autosave)
        SaveGame.save_for(request.user, default_data, slot=0)

        return Response({
            "detail": "New game started",
            "starting_scene": default_data["scene"]
        }, status=200)


class UserView(APIView):
    """
    GET /api/v1/auth/user/
    ─────────────────────
    Get current user information including staff status.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        print(f"🔍 UserView: request.user = {request.user}")
        print(f"🔍 UserView: user.is_staff = {request.user.is_staff}")
        serializer = UserSerializer(request.user)
        print(f"🔍 UserView: serializer.data = {serializer.data}")
        return Response(serializer.data)
