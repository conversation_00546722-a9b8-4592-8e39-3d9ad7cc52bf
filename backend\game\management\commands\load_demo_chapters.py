"""
Management command to load demo chapters and subchapters.

Creates:
* 2 chapters (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)
* 4 subchapters (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Konflik<PERSON>)
* Assigns existing scenes to subchapters

Idempotent – you can run it many times; existing rows are left untouched.
"""

from django.core.management.base import BaseCommand
from django.db import transaction

from game.models import Chapter, Subchapter, Scene


class Command(BaseCommand):
    help = "Load demo chapters and subchapters for development"

    def handle(self, *args, **options):
        with transaction.atomic():
            self.stdout.write("Creating demo chapters and subchapters...")

            # ───────────────── Chapters ─────────────────────
            chapter1, created = Chapter.objects.get_or_create(
                order=1,
                defaults={
                    'title': 'Początek',
                    'description': 'Pier<PERSON><PERSON> kroki w postapokaliptycznym świecie',
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f"✓ Created chapter: {chapter1}")

            chapter2, created = Chapter.objects.get_or_create(
                order=2,
                defaults={
                    'title': 'Mia<PERSON>',
                    'description': 'Eksploracja opuszczonego miasta',
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f"✓ Created chapter: {chapter2}")

            # ───────────────── Subchapters ─────────────────────
            subchapter1_1, created = Subchapter.objects.get_or_create(
                chapter=chapter1,
                order=1,
                defaults={
                    'title': 'Przebudzenie',
                    'description': 'Bohater budzi się w nieznanym miejscu',
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f"✓ Created subchapter: {subchapter1_1}")

            subchapter1_2, created = Subchapter.objects.get_or_create(
                chapter=chapter1,
                order=2,
                defaults={
                    'title': 'Pierwsze kroki',
                    'description': 'Nauka podstaw przetrwania',
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f"✓ Created subchapter: {subchapter1_2}")

            subchapter2_1, created = Subchapter.objects.get_or_create(
                chapter=chapter2,
                order=1,
                defaults={
                    'title': 'Eksploracja',
                    'description': 'Odkrywanie tajemnic miasta',
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f"✓ Created subchapter: {subchapter2_1}")

            subchapter2_2, created = Subchapter.objects.get_or_create(
                chapter=chapter2,
                order=2,
                defaults={
                    'title': 'Konflikt',
                    'description': 'Starcie z wrogami',
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f"✓ Created subchapter: {subchapter2_2}")

            # ───────────────── Assign existing scenes ─────────────────────
            # Assign zombie-forest to first subchapter
            try:
                zombie_scene = Scene.objects.get(slug='zombie-forest')
                if not zombie_scene.subchapter:
                    zombie_scene.subchapter = subchapter1_1
                    zombie_scene.save()
                    self.stdout.write(f"✓ Assigned scene '{zombie_scene.slug}' to {subchapter1_1}")
            except Scene.DoesNotExist:
                self.stdout.write("⚠ Scene 'zombie-forest' not found")

            # Assign other scenes if they exist
            scene_assignments = [
                ('street', subchapter1_1),
                ('alley', subchapter1_2),
                ('safe-house', subchapter1_2),
            ]

            for slug, subchapter in scene_assignments:
                try:
                    scene = Scene.objects.get(slug=slug)
                    if not scene.subchapter:
                        scene.subchapter = subchapter
                        scene.save()
                        self.stdout.write(f"✓ Assigned scene '{scene.slug}' to {subchapter}")
                except Scene.DoesNotExist:
                    self.stdout.write(f"⚠ Scene '{slug}' not found")

            self.stdout.write(
                self.style.SUCCESS(
                    f"\n🎉 Demo chapters loaded successfully!\n"
                    f"   Chapters: {Chapter.objects.count()}\n"
                    f"   Subchapters: {Subchapter.objects.count()}\n"
                    f"   Scenes with subchapters: {Scene.objects.filter(subchapter__isnull=False).count()}"
                )
            )
