# accounts/serializers.py
from dj_rest_auth.registration.serializers import RegisterSerializer
from rest_framework import serializers
from django.contrib.auth import get_user_model

User = get_user_model()

class EmailRegisterSerializer(RegisterSerializer):
    email = serializers.EmailField(required=True)
    username = serializers.CharField(required=False, allow_blank=True)
    password1 = serializers.CharField(write_only=True)
    password2 = serializers.CharField(write_only=True)
    
    def get_cleaned_data(self):
        return {
            'email': self.validated_data.get('email', ''),
            'username': self.validated_data.get('username', ''),
            'password1': self.validated_data.get('password1', ''),
            'password2': self.validated_data.get('password2', ''),
        }
    
    def save(self, request):
        # Let the parent class handle the registration with allauth
        # This ensures email verification works properly
        adapter = self.get_adapter()
        user = adapter.new_user(request)
        self.cleaned_data = self.get_cleaned_data()

        # Generate username if not provided
        username = self.cleaned_data.get('username', '')
        if not username:
            username = self.cleaned_data['email'].split('@')[0]

        # Handle username conflicts
        original_username = username
        counter = 1
        while User.objects.filter(username=username).exists():
            username = f"{original_username}_{counter}"
            counter += 1

        user.username = username
        user.email = self.cleaned_data['email']

        # Let allauth handle the email verification and user activation
        adapter.save_user(request, user, self)
        return user

    def get_adapter(self):
        from allauth.account.adapter import get_adapter
        return get_adapter()