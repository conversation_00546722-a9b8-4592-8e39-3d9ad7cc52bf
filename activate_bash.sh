#!/bin/bash
# Bash script to activate virtual environment for Git Bash
# Usage: source activate_bash.sh

# Navigate to project root
cd /f/Grimveil/rpg-project

# Source the activation script
source venv/Scripts/activate

echo "✅ Virtual environment activated!"
echo "🐍 Python version: $(python --version)"
echo "📦 Pip version: $(pip --version | cut -d' ' -f1-2)"
echo ""
echo "You can now run Django commands like:"
echo "  python backend/manage.py runserver"
echo "  python backend/manage.py migrate"
echo "  python backend/manage.py test"
echo ""
