# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-10 12:48+0000\n"
"PO-Revision-Date: 2020-05-10 15:04+0200\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Last-Translator: \n"
"Language-Team: \n"
"X-Generator: Poedit 2.2.4\n"

#: rest_auth/rest_auth/registration/serializers.py:67
msgid "View is not defined, pass it as a context variable"
msgstr "Vyn definieras inte, skicka den som en kontextvariabel"

#: rest_auth/rest_auth/registration/serializers.py:72
msgid "Define adapter_class in view"
msgstr "Definiera “adapter_class” i vyn"

#: rest_auth/rest_auth/registration/serializers.py:91
msgid "Define callback_url in view"
msgstr "Definiera “callback_url” i vyn"

#: rest_auth/rest_auth/registration/serializers.py:95
msgid "Define client_class in view"
msgstr "Definiera “client_class” i vyn"

#: rest_auth/rest_auth/registration/serializers.py:116
msgid "Incorrect input. access_token or code is required."
msgstr "Felaktig inmatning. access_token eller code krävs."

#: rest_auth/rest_auth/registration/serializers.py:125
msgid "Incorrect value"
msgstr "Felaktigt värde"

#: rest_auth/rest_auth/registration/serializers.py:139
msgid "User is already registered with this e-mail address."
msgstr "Användaren är redan registrerad med den här e-postadressen."

#: rest_auth/rest_auth/registration/serializers.py:185
msgid "A user is already registered with this e-mail address."
msgstr "En användare är redan registrerad med den här e-postadressen."

#: rest_auth/rest_auth/registration/serializers.py:193
msgid "The two password fields didn't match."
msgstr "De två lösenordsfälten matchade inte."

#: rest_auth/rest_auth/registration/views.py:51
msgid "Verification e-mail sent."
msgstr "Verifikationsmail har skickats."

#: rest_auth/rest_auth/registration/views.py:98
msgid "ok"
msgstr "ok"

#: rest_auth/rest_auth/serializers.py:33
msgid "Must include \"email\" and \"password\"."
msgstr "Måste inkludera “email” och “password”."

#: rest_auth/rest_auth/serializers.py:44
msgid "Must include \"username\" and \"password\"."
msgstr "Måste innehålla “username” och “password”."

#: rest_auth/rest_auth/serializers.py:57
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr "Måste innehålla antingen “username” eller “email” och “password”."

#: rest_auth/rest_auth/serializers.py:98
msgid "User account is disabled."
msgstr "Användarkontot är avstängt."

#: rest_auth/rest_auth/serializers.py:101
msgid "Unable to log in with provided credentials."
msgstr "Det går inte att logga in med de angivna uppgifterna."

#: rest_auth/rest_auth/serializers.py:110
msgid "E-mail is not verified."
msgstr "E-post är inte verifierad."

#: rest_auth/rest_auth/serializers.py:259
msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "Ditt gamla lösenord angavs felaktigt. Vänligen ange det igen."

#: rest_auth/rest_auth/views.py:137
msgid "Successfully logged out."
msgstr "Utloggad."

#: rest_auth/rest_auth/views.py:190
msgid "Password reset e-mail has been sent."
msgstr "E-post för återställning av lösenord har skickats."

#: rest_auth/rest_auth/views.py:216
msgid "Password has been reset with the new password."
msgstr "Lösenordet har återställts med det nya lösenordet."

#: rest_auth/rest_auth/views.py:238
msgid "New password has been saved."
msgstr "Nytt lösenord har sparats."
