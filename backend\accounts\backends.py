from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from allauth.account.models import EmailAddress

User = get_user_model()


class EmailVerificationBackend(ModelBackend):
    """
    Custom authentication backend that requires email verification
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        # Get the user using the default authentication
        user = super().authenticate(request, username=username, password=password, **kwargs)
        
        if user is None:
            return None
        
        # Check if email verification is required and if user's email is verified
        try:
            email_address = EmailAddress.objects.get(user=user, primary=True)
            if not email_address.verified:
                # Return None to prevent login for unverified users
                return None
        except EmailAddress.DoesNotExist:
            # If no EmailAddress record exists, user hasn't verified email
            return None
        
        return user
