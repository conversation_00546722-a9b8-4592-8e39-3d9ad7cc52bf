# Generated by Django 5.2.1 on 2025-06-20 09:23

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0010_actionmeta_failure_scene_actionmeta_success_scene'),
    ]

    operations = [
        migrations.AddField(
            model_name='perk',
            name='effect_tags',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=32), blank=True, default=list, help_text='Tagi na które wpływa efekt', size=None),
        ),
        migrations.AddField(
            model_name='perk',
            name='effect_type',
            field=models.CharField(choices=[('roll_bonus', 'Bonus do rzutów'), ('attr_growth', 'Szy<PERSON>zy rozwój atrybutów'), ('skill_bonus', 'Łatwiejsze testy'), ('main_xp_bonus', '<PERSON><PERSON><PERSON><PERSON> rozwój postaci'), ('unlock_actions', 'Nowe akcje'), ('special', 'Efekt specjalny')], default='roll_bonus', help_text='Typ efektu perka', max_length=20),
        ),
        migrations.AddField(
            model_name='perk',
            name='effect_value',
            field=models.IntegerField(default=0, help_text='Wartość efektu (np. 10 dla +10%)'),
        ),
        migrations.AddField(
            model_name='perk',
            name='special_config',
            field=models.JSONField(blank=True, default=dict, help_text='Konfiguracja specjalnych efektów'),
        ),
    ]
