# game/api/rules.py
from django.contrib.auth import get_user_model
from game.logic.dice import action_test
from game.logic.xp import apply_xp_growth

User = get_user_model()


def handle_action(action_meta, user, situational_mod=0, chosen_skill=None):
    """
    Complete action handling with dice rolling and XP growth.

    Args:
        action_meta: ActionMeta instance
        user: User instance
        situational_mod: Situational modifier (int)
        chosen_skill: Optional skill code chosen by player

    Returns:
        Dict with outcome, roll results, XP changes, and optional scene transition
    """
    # Perform dice roll
    roll_result = action_test(action_meta, user, situational_mod, chosen_skill)

    # Apply XP growth based on outcome
    xp_changes = apply_xp_growth(user, action_meta, roll_result["outcome"])

    # Determine scene transition based on outcome
    next_scene = None
    if action_meta.action_type == "skill_test":
        outcome = roll_result["outcome"]
        if outcome in ["success", "critical"]:
            next_scene = action_meta.success_scene
        elif outcome in ["fail", "partial_fail"]:
            next_scene = action_meta.failure_scene



    # Combine results
    result = {
        **roll_result,
        "xp_changes": xp_changes,
        "next_scene": next_scene,
    }

    return result
