# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-28 11:41+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: registration/serializers.py:67
msgid "View is not defined, pass it as a context variable"
msgstr ""

#: registration/serializers.py:72
msgid "Define adapter_class in view"
msgstr ""

#: registration/serializers.py:91
msgid "Define callback_url in view"
msgstr ""

#: registration/serializers.py:95
msgid "Define client_class in view"
msgstr ""

#: registration/serializers.py:116
msgid "Incorrect input. access_token or code is required."
msgstr ""

#: registration/serializers.py:125
msgid "Incorrect value"
msgstr ""

#: registration/serializers.py:139
msgid "User is already registered with this e-mail address."
msgstr ""

#: registration/serializers.py:185
msgid "A user is already registered with this e-mail address."
msgstr ""

#: registration/serializers.py:193
msgid "The two password fields didn't match."
msgstr ""

#: registration/views.py:51
msgid "Verification e-mail sent."
msgstr ""

#: registration/views.py:98
msgid "ok"
msgstr ""

#: serializers.py:33
msgid "Must include \"email\" and \"password\"."
msgstr ""

#: serializers.py:44
msgid "Must include \"username\" and \"password\"."
msgstr ""

#: serializers.py:57
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr ""

#: serializers.py:98
msgid "User account is disabled."
msgstr ""

#: serializers.py:101
msgid "Unable to log in with provided credentials."
msgstr ""

#: serializers.py:110
msgid "E-mail is not verified."
msgstr ""

#: views.py:127
msgid "Successfully logged out."
msgstr ""

#: views.py:175
msgid "Password reset e-mail has been sent."
msgstr ""

#: views.py:201
msgid "Password has been reset with the new password."
msgstr ""

#: views.py:223
msgid "New password has been saved."
msgstr ""
