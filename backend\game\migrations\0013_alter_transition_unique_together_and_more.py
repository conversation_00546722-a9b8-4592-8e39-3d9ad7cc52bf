# Generated by Django 5.2.1 on 2025-06-22 16:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0012_chapter_subchapter_scene_subchapter'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='transition',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='transition',
            name='target_chapter',
            field=models.ForeignKey(blank=True, help_text="Rozdział docelowy (dla transition_type='chapter')", null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_transitions', to='game.chapter'),
        ),
        migrations.AddField(
            model_name='transition',
            name='target_subchapter',
            field=models.ForeignKey(blank=True, help_text="Podrozdział docelowy (dla transition_type='subchapter')", null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_transitions', to='game.subchapter'),
        ),
        migrations.AddField(
            model_name='transition',
            name='transition_type',
            field=models.CharField(choices=[('scene', 'Przejście do sceny'), ('chapter', 'Przejście do rozdziału'), ('subchapter', 'Przejście do podrozdziału')], default='scene', help_text='Typ przejścia - do sceny, rozdziału lub podrozdziału', max_length=20),
        ),
        migrations.AlterField(
            model_name='transition',
            name='target',
            field=models.ForeignKey(blank=True, help_text="Scena docelowa (dla transition_type='scene')", null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming', to='game.scene'),
        ),
    ]
