#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Python 3.12 and pip
sudo apt-get install -y python3.12 python3.12-venv python3.12-dev python3-pip

# Install Node.js 20 (required for frontend)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL client and development libraries
sudo apt-get install -y postgresql-client libpq-dev

# Install Docker and Docker Compose
sudo apt-get install -y docker.io docker-compose-v2
sudo systemctl start docker
sudo usermod -aG docker $USER

# Create Python virtual environment for backend
cd backend
python3.12 -m venv venv
source venv/bin/activate

# Create requirements.txt based on Django settings analysis
cat > requirements.txt << 'EOF'
Django==5.1.4
djangorestframework==3.15.2
django-cors-headers==4.6.0
django-allauth==65.3.0
dj-rest-auth==7.0.0
djangorestframework-simplejwt==5.3.1
psycopg2-binary==2.9.10
python-dotenv==1.0.1
gunicorn==23.0.0
whitenoise==6.8.2
pytest==8.3.4
pytest-django==4.9.0
EOF

# Install Python dependencies
pip install --upgrade pip
pip install -r requirements.txt

# Add virtual environment activation to profile
echo "source $(pwd)/venv/bin/activate" >> $HOME/.profile

# Set up environment variables for testing
export DJANGO_SETTINGS_MODULE=config.settings
export POSTGRES_DB=rpg
export POSTGRES_USER=rpg_user  
export POSTGRES_PASSWORD=rpg_pass
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export DJANGO_SECRET_KEY=django-insecure-test-key
export DJANGO_DEBUG=True
export DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1

# Start PostgreSQL service for testing
sudo apt-get install -y postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create test database and user
sudo -u postgres psql -c "CREATE USER rpg_user WITH PASSWORD 'rpg_pass';"
sudo -u postgres psql -c "CREATE DATABASE rpg OWNER rpg_user;"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE rpg TO rpg_user;"

# Run Django migrations
python manage.py migrate

# Go to frontend directory and install dependencies
cd ../frontend/rpg-client
npm ci

# Add npm to PATH
echo 'export PATH=$PATH:/usr/bin' >> $HOME/.profile

# Return to root directory
cd ../..