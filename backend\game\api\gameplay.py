# ─────────────────────────── gameplay endpoints ─────────────────────
from rest_framework import permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.exceptions import ValidationError
from django.db import transaction

from game.logic.resolver import resolve_transitions
from game.logic.context import build_context
from game.utils.logging import Game<PERSON>og<PERSON>, log_api_operation, log_api_error
from game.utils.exceptions import GameValidationError, GameNotFoundError

from ..models import (
    Attribute, Subskill, Perk,
    Scene, GameObject, Transition,
    ActionMeta, ActionLog, SaveGame
)

class RollActionView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    @transaction.atomic
    def post(self, request):
        """Execute action roll with proper validation and logging."""
        try:
            from .rules import handle_action
            from game.logic.xp import is_action_executed, mark_action_executed

            action_id = request.data.get("action_id")
            scene_slug = request.data.get("scene", "street")
            situational = int(request.data.get("situational_mod", 0))
            chosen_skill = request.data.get("chosen_skill")

            # Validate required fields
            if not action_id:
                raise GameValidationError("Action ID is required", field="action_id")

            log_api_operation(
                "ROLL_ACTION", "ActionMeta",
                user=request.user,
                action_id=action_id,
                scene_slug=scene_slug,
                situational_mod=situational
            )

            # ActionMeta lookup
            try:
                action_meta = ActionMeta.objects.get(pk=action_id)
            except ActionMeta.DoesNotExist:
                raise GameNotFoundError(f"Action {action_id} not found", resource="ActionMeta")

            # Check if action already executed (only for skill tests)
            if action_meta.action_type == 'skill_test' and is_action_executed(request.user, scene_slug, action_id):
                GameLogger.warning(
                    "Action already executed",
                    user=request.user,
                    action_id=action_id,
                    scene_slug=scene_slug
                )
                return Response({
                    'error': True,
                    'message': f"Action {action_id} already executed in scene {scene_slug}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Scene fallback
            scene, created = Scene.objects.get_or_create(
                slug=scene_slug,
                defaults={"title": scene_slug.capitalize()}
            )

            if created:
                GameLogger.info(f"Created fallback scene: {scene_slug}", user=request.user)

            # Execute action roll
            result = handle_action(action_meta, request.user, situational, chosen_skill)

            # Mark action as executed (only skill tests)
            mark_action_executed(request.user, scene_slug, action_id, action_meta.action_type)

            # Update scene in save data
            from game.logic.xp import get_player_save_data
            save_data = get_player_save_data(request.user)
            save_data["scene"] = scene_slug
            save_data["last_outcome"] = result["outcome"]
            save_data["last_action_result"] = {
                "result": result,
                "action_id": action_id,
                "action_name": action_meta.label,
                "scene_slug": scene_slug
            }

            SaveGame.save_for(request.user, save_data, slot=0)

            # Log the action
            ActionLog.objects.create(
                action=action_meta,
                scene=scene,
                outcome=result["outcome"],
                roll_value=result["R"],
                target_value=result["T"],
            )

            GameLogger.info(
                "Action executed successfully",
                user=request.user,
                action_id=action_id,
                outcome=result["outcome"],
                roll_value=result["R"]
            )

            return Response(result)

        except (GameValidationError, GameNotFoundError) as e:
            log_api_error("ROLL_ACTION", "ActionMeta", e.message, user=request.user)
            return Response({
                'error': True,
                'message': e.message
            }, status=e.status_code)

        except ValidationError as e:
            log_api_error("ROLL_ACTION", "ActionMeta", str(e), user=request.user)
            return Response({
                'error': True,
                'message': 'Invalid action data',
                'details': e.detail if hasattr(e, 'detail') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            log_api_error("ROLL_ACTION", "ActionMeta", str(e), user=request.user)
            return Response({
                'error': True,
                'message': 'Failed to execute action'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ActionSkillsView(APIView):
    """Get available skills for an action."""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        action_id = request.query_params.get("action_id")

        if not action_id:
            return Response({"error": "action_id required"}, status=400)

        try:
            action_meta = ActionMeta.objects.get(pk=action_id)
        except ActionMeta.DoesNotExist:
            return Response({"error": f"Action {action_id} not found"}, status=404)

        # Get player stats
        from game.logic.xp import get_player_save_data, SKILL_NAMES
        save_data = get_player_save_data(request.user)
        player_stats = {"skills": save_data.get("subskills", {})}

        # Get available skills with their levels
        available_skills = action_meta.get_available_skills_for_player(player_stats)
        skill_details = []

        for skill_code in available_skills:
            skill_data = save_data.get("subskills", {}).get(skill_code, {})
            skill_details.append({
                "code": skill_code,
                "name": SKILL_NAMES.get(skill_code, skill_code),
                "level": skill_data.get("value", 0)
            })

        return Response({
            "action_id": action_id,
            "action_name": action_meta.label,
            "available_skills": skill_details,
            "primary_skill": action_meta.get_primary_skill(),
            "allow_choice": action_meta.allow_skill_choice and len(available_skills) > 1
        })


class LevelUpView(APIView):
    """
    POST /api/v1/play/levelup/
    ─────────────────────────
    Level up character and optionally choose a perk.

    Request body:
    {
        "perk_id": "quick_reflexes"  // optional
    }
    """
    permission_classes = [permissions.IsAuthenticated]

    @transaction.atomic
    def post(self, request):
        from game.logic.xp import level_up_character

        perk_id = request.data.get("perk_id")
        result = level_up_character(request.user, perk_id)

        if not result["success"]:
            return Response(result, status=400)

        return Response(result)


class AvailablePerksView(APIView):
    """
    GET /api/v1/play/available-perks/
    ─────────────────────────────────
    Get available perks for player's current level.

    Response:
    {
        "level": 2,
        "available_perks": [
            {
                "id": "quick_reflexes",
                "name": "Szybki refleks",
                "description": "+10% szansy na unik...",
                "tags": ["dex", "evasion"]
            },
            ...
        ]
    }
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        from game.logic.xp import get_player_save_data
        import random

        save_data = get_player_save_data(request.user)
        player_level = save_data.get("level", 1)
        chosen_perks = save_data.get("chosen_perks", [])
        gameplay_tags = save_data.get("gameplay_tags", {})

        # Get perks available for this level
        available_perks = Perk.objects.filter(
            level_min__lte=player_level,
            level_max__gte=player_level
        ).exclude(id__in=chosen_perks)

        # Convert to list for weighted selection
        perk_pool = list(available_perks)

        # Create weighted list (perks matching gameplay tags get 3x weight)
        weighted_perks = []
        for perk in perk_pool:
            weight = 3 if any(tag in gameplay_tags and gameplay_tags[tag] > 0 for tag in perk.tags) else 1
            weighted_perks.extend([perk] * weight)

        # Select 3 random perks (without duplicates)
        selected_perks = []
        available_weighted = weighted_perks.copy()

        while len(selected_perks) < 3 and available_weighted:
            # Pick random perk
            chosen_perk = random.choice(available_weighted)

            # Add to selection if not already selected
            if not any(p.id == chosen_perk.id for p in selected_perks):
                selected_perks.append(chosen_perk)

            # Remove all instances of this perk from weighted list
            available_weighted = [p for p in available_weighted if p.id != chosen_perk.id]

        # Format response
        perk_data = []
        for perk in selected_perks:
            perk_data.append({
                "id": perk.id,
                "name": perk.name,
                "description": perk.description,
                "level_min": perk.level_min,
                "level_max": perk.level_max,
                "tags": perk.tags
            })

        return Response({
            "level": player_level,
            "available_perks": perk_data,
            "chosen_perks": chosen_perks,
            "gameplay_tags": gameplay_tags
        })


class AttributeProgressView(APIView):
    """
    GET /api/v1/play/progress/
    ──────────────────────────
    Get detailed progress information for all attributes.

    Response:
    {
        "STR": {
            "current_xp": 3,
            "next_threshold": 5,
            "progress_percent": 60.0,
            "current_level": 1
        },
        ...
    }
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        from game.logic.xp import get_player_save_data, get_attribute_progress_info

        save_data = get_player_save_data(request.user)
        progress_info = {}

        for attr_code, attr_data in save_data["attributes"].items():
            progress_info[attr_code] = get_attribute_progress_info(attr_data)

        return Response(progress_info)


class AttributeSkillChoiceView(APIView):
    """
    POST /api/v1/play/attribute-skill/
    ──────────────────────────────────
    When an attribute levels up, player can choose which skill to improve.

    Request:
    {
        "attribute": "STR",
        "chosen_skill": "melee"
    }

    Response:
    {
        "success": true,
        "skill_improved": "melee",
        "new_skill_level": 3
    }
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        from game.logic.xp import get_player_save_data, ATTRIBUTE_SKILL_MAPPING, add_xp_to_subskill

        attribute = request.data.get("attribute")
        chosen_skill = request.data.get("chosen_skill")

        if not attribute or not chosen_skill:
            return Response({"error": "Missing attribute or chosen_skill"}, status=400)

        # Validate attribute exists
        if attribute not in ATTRIBUTE_SKILL_MAPPING:
            return Response({"error": f"Invalid attribute: {attribute}"}, status=400)

        # Validate skill is available for this attribute
        available_skills = ATTRIBUTE_SKILL_MAPPING[attribute]
        if chosen_skill not in available_skills:
            return Response({
                "error": f"Skill {chosen_skill} not available for attribute {attribute}",
                "available_skills": available_skills
            }, status=400)

        # Get save data
        save_data = get_player_save_data(request.user)

        # Check if skill exists in save data
        if chosen_skill not in save_data["subskills"]:
            return Response({"error": f"Skill {chosen_skill} not found in save data"}, status=400)

        # Add 1 full level to chosen skill (direct level increase)
        skill_data = save_data["subskills"][chosen_skill]
        old_level = skill_data["value"]

        # Directly increase skill level by 1 (no XP needed)
        skill_data["value"] += 1
        new_level = skill_data["value"]
        leveled_up = True

        # Save updated data
        from game.models import SaveGame
        SaveGame.save_for(request.user, save_data, slot=0)

        return Response({
            "success": True,
            "skill_improved": chosen_skill,
            "old_skill_level": old_level,
            "new_skill_level": new_level,
            "leveled_up": leveled_up
        })


def handle_action(request, action_meta, scene, result_dict):
    """
    Przykład: po przeliczeniu akcji wynik ląduje w result_dict,
    a my zapisujemy autosave.
    """
    # … logika akcji …

    # AUTOSAVE (slot 0)
    SaveGame.save_for(
        request.user,
        {"scene": scene.slug, "xp": request.user.profile_xp, "extra": result_dict},
        slot=0,
    )
    return result_dict

class PlayResolveView(APIView):
    """
    GET /api/v1/play/resolve/?scene=<slug>
    ──────────────────────────────────────
    Returns a list of transitions that *this* player can currently take
    from the given scene.

    Response example:
    {
        "scene": "street",
        "transitions": [
            { "id": 7, "target": "alley", "label": "Wejście do zaułka", "auto": false },
            …
        ]
    }
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        scene_slug = request.query_params.get("scene")
        if not scene_slug:
            return Response(
                {"detail": "Missing `scene` query parameter."}, status=400
            )

        transitions = resolve_transitions(scene_slug, request.user)
        payload = []
        for tr in transitions:
            transition_data = {
                "id": tr.id,
                "transition_type": tr.transition_type,
                "label": tr.description,
                "auto": tr.auto,
            }

            # Add appropriate target based on transition type
            if tr.transition_type == 'scene' and tr.target:
                transition_data["target"] = tr.target.slug
                transition_data["target_type"] = "scene"
            elif tr.transition_type == 'chapter' and tr.target_chapter:
                transition_data["target"] = tr.target_chapter.id
                transition_data["target_type"] = "chapter"
                transition_data["target_title"] = tr.target_chapter.title
            elif tr.transition_type == 'subchapter' and tr.target_subchapter:
                transition_data["target"] = tr.target_subchapter.id
                transition_data["target_type"] = "subchapter"
                transition_data["target_title"] = tr.target_subchapter.title

            payload.append(transition_data)

        return Response({
            "origin": scene_slug,
            "transitions": payload,
        })


class PlayTransitionView(APIView):
    """
    POST /api/v1/play/transition/
    ──────────────────────────────
    Execute a transition to move player to a new scene/chapter/subchapter.

    Request body:
    {
        "transition_id": 123
    }

    Response:
    {
        "success": true,
        "new_scene": "new-scene-slug",
        "message": "Przeszedłeś do nowej sceny"
    }
    """
    permission_classes = [permissions.IsAuthenticated]

    @transaction.atomic
    def post(self, request):
        transition_id = request.data.get("transition_id")
        if not transition_id:
            return Response(
                {"detail": "Missing transition_id"}, status=400
            )

        try:
            transition = Transition.objects.select_related(
                "origin", "target", "target_chapter", "target_subchapter"
            ).get(id=transition_id)
        except Transition.DoesNotExist:
            return Response(
                {"detail": "Transition not found"}, status=404
            )

        # Verify player can use this transition
        available_transitions = resolve_transitions(transition.origin.slug, request.user)
        if transition not in available_transitions:
            return Response(
                {"detail": "Transition not available"}, status=403
            )

        # Execute transition based on type
        if transition.transition_type == 'scene' and transition.target:
            new_scene_slug = transition.target.slug
            message = f"Przeszedłeś do: {transition.target.title}"

        elif transition.transition_type == 'chapter' and transition.target_chapter:
            # Find first scene in the chapter (first scene of first subchapter)
            first_subchapter = transition.target_chapter.subchapters.filter(is_active=True).order_by('order').first()
            if first_subchapter:
                first_scene = first_subchapter.scenes.filter(is_active=True).order_by('id').first()
                if first_scene:
                    new_scene_slug = first_scene.slug
                    message = f"Przeszedłeś do rozdziału: {transition.target_chapter.title}"
                else:
                    return Response(
                        {"detail": "No scenes found in target chapter"}, status=400
                    )
            else:
                return Response(
                    {"detail": "No subchapters found in target chapter"}, status=400
                )

        elif transition.transition_type == 'subchapter' and transition.target_subchapter:
            # Find first scene in the subchapter
            first_scene = transition.target_subchapter.scenes.filter(is_active=True).order_by('id').first()
            if first_scene:
                new_scene_slug = first_scene.slug
                message = f"Przeszedłeś do podrozdziału: {transition.target_subchapter.title}"
            else:
                return Response(
                    {"detail": "No scenes found in target subchapter"}, status=400
                )
        else:
            return Response(
                {"detail": "Invalid transition configuration"}, status=400
            )

        # Update player's save game
        from ..models import SaveGame
        current_save = SaveGame.load_for(request.user, slot=0)

        # Check if we're entering a new subchapter for autosave
        old_scene_slug = current_save.get("scene")
        is_new_subchapter = False

        if old_scene_slug:
            try:
                from ..models import Scene
                old_scene = Scene.objects.select_related('subchapter').get(slug=old_scene_slug)
                new_scene = Scene.objects.select_related('subchapter').get(slug=new_scene_slug)

                # Check if subchapter changed
                if (old_scene.subchapter and new_scene.subchapter and
                    old_scene.subchapter.id != new_scene.subchapter.id):
                    is_new_subchapter = True
                    print(f"🔄 Autosave: Entering new subchapter {new_scene.subchapter.title}")
            except Scene.DoesNotExist:
                pass

        current_save["scene"] = new_scene_slug
        current_save["current_scene_session"] = new_scene_slug  # Reset scene session

        # Clear executed actions for new scene
        if "executed_actions" not in current_save:
            current_save["executed_actions"] = {}
        current_save["executed_actions"][new_scene_slug] = []
        current_save["last_action_result"] = None

        SaveGame.save_for(request.user, current_save, slot=0)

        return Response({
            "success": True,
            "new_scene": new_scene_slug,
            "message": message,
            "transition_type": transition.transition_type
        })