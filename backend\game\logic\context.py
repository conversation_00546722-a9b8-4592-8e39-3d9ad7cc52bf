"""
game.logic.context
~~~~~~~~~~~~~~~~~~

`build_context(player, *, scene=None, extra=None)` returns a dictionary with
_everything the rules engine might want to reference_ in a *condition_expr*.

The function is intentionally defensive:
- **Absent relations** are ignored (no hard-fails in tests).
- `SaveGame` / attribute tables are queried only if the corresponding app
  pieces exist; otherwise they’re skipped gracefully.
"""

from __future__ import annotations

import json
import gzip
from typing import Any, Mapping

from django.contrib.auth import get_user_model
from django.db.models import Prefetch

from game.models import Attribute, SaveGame  # SaveGame is guaranteed (_migration 2.2_)

User = get_user_model()


# ---------------------------------------------------------------------------


def _safe_load(data: bytes | memoryview | str | None) -> dict[str, Any]:
    """Try JSON-decode (and gunzip when needed); fallback → {}."""
    if not data:
        return {}
    try:
        if isinstance(data, (bytes, memoryview)):
            data = gzip.decompress(data).decode("utf-8")
        return json.loads(data)
    except Exception:  # pragma: no cover
        return {}


def build_context(
    player: User,
    *,
    scene: str | None = None,
    extra: Mapping[str, Any] | None = None,
) -> dict[str, Any]:
    """
    Aggregate a **flat namespace** for expression evaluation.

    Order of precedence (later wins):
        1. auto-derived player flags/attributes
        2. autosave (slot 0) payload
        3. `extra` overrides supplied by caller

    Keys produced *out of the box*:

    | key                | type | source                                    |
    |--------------------|------|-------------------------------------------|
    | `id`, `username`   | int / str | `auth_user` fields                |
    | `is_staff`         | bool | `auth_user`                               |
    | attribute codes    | int / float | via `player.attr_values.*` fallback  |
    | `current_scene`    | str | autosave slot 0 (if exists)                |
    | _custom from save_ | any | everything embedded by the game loop       |
    """
    ctx: dict[str, Any] = {
        "id": player.id,
        "username": player.username,
        "is_staff": player.is_staff,
    }

    # ── 1) Attributes (if a linking table exists) ─────────────────────────
    #
    # By convention we assume a model:
    #   class PlayerAttribute(models.Model):
    #       player = ForeignKey(settings.AUTH_USER_MODEL, …, related_name="attr_values")
    #       attribute = ForeignKey(Attribute, …)
    #       value = models.IntegerField()
    #
    # If the project has not implemented it yet, this block is silently skipped.
    #
    try:
        # Prefetch related Attribute objects in one go (efficient in prod code);
        # in tests we don’t care if it falls back to an empty queryset.
        qs = (
            player.attr_values.select_related("attribute")
            if hasattr(player, "attr_values")
            else []
        )
        for row in qs:  # type: ignore[attr-defined]
            ctx[row.attribute.code] = row.value
    except Exception:  # pragma: no cover
        pass

    # ── 2) Latest autosave (slot 0) ───────────────────────────────────────
    save = SaveGame.latest_for(player, slot=0)
    autosave_payload = _safe_load(save.data) if save else {}
    ctx.update(autosave_payload)
    if scene:
        # allow caller to force scene context
        ctx.setdefault("current_scene", scene)
    elif "scene" in autosave_payload:
        ctx["current_scene"] = autosave_payload["scene"]

    # ── 3) Explicit overrides ────────────────────────────────────────────
    if extra:
        ctx.update(extra)

    return ctx
