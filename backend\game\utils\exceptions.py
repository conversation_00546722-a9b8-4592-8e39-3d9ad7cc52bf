"""
Custom exception handling for the game application.
"""
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework.exceptions import ValidationError
import logging

logger = logging.getLogger('game')


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides structured error responses
    and proper logging.
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # Get request info for logging
    request = context.get('request')
    view = context.get('view')
    
    view_name = view.__class__.__name__ if view else 'Unknown'
    path = request.path if request else 'Unknown'
    user = request.user if request and hasattr(request, 'user') else None
    
    if response is not None:
        # Log the error with context
        logger.error(
            f"API Error in {view_name}: {exc}",
            extra={
                'view': view_name,
                'path': path,
                'user_id': user.id if user and hasattr(user, 'id') else None,
                'status_code': response.status_code,
                'exception_type': exc.__class__.__name__
            }
        )
        
        # Customize error response format
        custom_response_data = {
            'error': True,
            'message': get_error_message(exc),
            'code': exc.__class__.__name__,
            'details': response.data if isinstance(response.data, dict) else {'detail': response.data}
        }
        
        # Don't expose sensitive information in production
        if hasattr(exc, 'detail') and not should_expose_details(exc):
            custom_response_data['details'] = {'detail': 'An error occurred'}
        
        response.data = custom_response_data
    
    else:
        # Handle exceptions not caught by DRF
        logger.error(
            f"Unhandled exception in {view_name}: {exc}",
            extra={
                'view': view_name,
                'path': path,
                'user_id': user.id if user and hasattr(user, 'id') else None,
                'exception_type': exc.__class__.__name__
            }
        )
        
        # Return generic error response
        response = Response({
            'error': True,
            'message': 'An unexpected error occurred',
            'code': 'InternalServerError',
            'details': {'detail': 'Please try again later'}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return response


def get_error_message(exc):
    """Get user-friendly error message based on exception type."""
    if isinstance(exc, ValidationError):
        return "Invalid data provided"
    elif isinstance(exc, DjangoValidationError):
        return "Data validation failed"
    elif hasattr(exc, 'detail'):
        if isinstance(exc.detail, str):
            return exc.detail
        elif isinstance(exc.detail, dict):
            # Try to get a meaningful message from detail dict
            if 'detail' in exc.detail:
                return exc.detail['detail']
            else:
                return "Validation error occurred"
    
    return "An error occurred"


def should_expose_details(exc):
    """Determine if exception details should be exposed to the client."""
    # Only expose details for validation errors and other "safe" exceptions
    safe_exceptions = (ValidationError, DjangoValidationError)
    return isinstance(exc, safe_exceptions)


class GameAPIException(Exception):
    """Base exception for game API operations."""
    
    def __init__(self, message: str, code: str = None, status_code: int = 400):
        self.message = message
        self.code = code or self.__class__.__name__
        self.status_code = status_code
        super().__init__(message)


class GameValidationError(GameAPIException):
    """Exception for game-specific validation errors."""
    
    def __init__(self, message: str, field: str = None):
        self.field = field
        super().__init__(message, 'ValidationError', 400)


class GamePermissionError(GameAPIException):
    """Exception for game-specific permission errors."""
    
    def __init__(self, message: str = "Permission denied"):
        super().__init__(message, 'PermissionError', 403)


class GameNotFoundError(GameAPIException):
    """Exception for game-specific not found errors."""
    
    def __init__(self, message: str, resource: str = None):
        self.resource = resource
        super().__init__(message, 'NotFoundError', 404)
