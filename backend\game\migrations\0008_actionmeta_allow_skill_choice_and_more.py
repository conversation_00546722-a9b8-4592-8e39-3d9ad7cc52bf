# Generated by Django 5.2.1 on 2025-06-19 16:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0007_scene_mood'),
    ]

    operations = [
        migrations.AddField(
            model_name='actionmeta',
            name='allow_skill_choice',
            field=models.BooleanField(default=True, help_text='<PERSON><PERSON> gracz może wybiera<PERSON> umiej<PERSON>, czy używa tylko primary_skill'),
        ),
        migrations.AddField(
            model_name='actionmeta',
            name='available_skills',
            field=models.JSONField(blank=True, default=list, help_text="Lista kodów umiejętności dostępnych dla tej akcji (np. ['firearms', 'precision_shooting'])"),
        ),
        migrations.AddField(
            model_name='actionmeta',
            name='primary_skill',
            field=models.CharField(blank=True, help_text='Kod głównej umiej<PERSON> (auto-wybierana jeśli gracz nie wybierze)', max_length=50),
        ),
        migrations.AlterField(
            model_name='actionmeta',
            name='subskill',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='game.subskill'),
        ),
    ]
