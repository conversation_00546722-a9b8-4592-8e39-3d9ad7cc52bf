# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-04-27 05:02+0000\n"
"Last-Translator: jFel63 <<EMAIL>>\n"
"Language-Team: Ukrainian <https://hosted.weblate.org/projects/allauth/django-"
"allauth/uk/>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"
"X-Generator: Weblate 5.12-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Цей обліковий запис наразі неактивний."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Ви не можете видалити свою основну адресу електронної пошти."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Ця адреса електронної пошти вже пов'язана з цим обліковим записом."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Вказана адреса електронної пошти та/або пароль неправильні."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Вказано невірний номер телефону або пароль."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Користувач з цією адресою електронної пошти вже зареєстрований."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Будь ласка, введіть свій поточний пароль."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Неправильний код."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Неправильний пароль."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Недійсний або прострочений ключ."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "Недійсний токен."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Токен відновлення пароля недійсний."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Ви не можете додати більше ніж %d адрес електронної пошти."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Користувач з цією адресою електронної пошти вже зареєстрований."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Занадто багато невдалих спроб входу. Спробуйте ще раз пізніше."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr ""
"Ця адреса електронної пошти не призначена для жодного облікового запису."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Цей номер телефону не призначено для жодного облікового запису."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Ваша основна адреса електронної пошти повинна бути підтверджена."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Ім'я користувача не може бути використане. Будь ласка, виберіть інше ім'я "
"користувача."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Вказане ім'я користувача або пароль неправильні."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Нове значення має відрізнятись від поточного."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Використовуйте свій пароль"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Використовуйте додаток для аутентифікації або код"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Використовуйте ключ безпеки"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Позначте обрані адреси електронної пошти як підтверджені"

#: account/apps.py:11
msgid "Accounts"
msgstr "Облікові записи"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"Введіть номер телефону, включаючи код країни (наприклад +38 для України)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Номер телефону"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Вам потрібно вводити один і той самий пароль кожен раз."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Пароль"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Запам'ятати Мене"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Адреса електронної пошти"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "Електронна пошта"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Ім'я користувача"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Вхід"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Ім'я користувача, електронна пошта або номер телефону"

#: account/forms.py:156
msgid "Username or email"
msgstr "Ім'я користувача або електронна пошта"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Ім'я користувача або номер телефону"

#: account/forms.py:160
msgid "Email or phone"
msgstr "Електронна пошта або номер телефону"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Забули пароль?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "Електронна пошта (ще раз)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Підтвердження адреси електронної пошти"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "Електронна пошта (необов'язково)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Ім'я користувача (необов'язково)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Ви повинні вводити одну й ту ж електронну пошту кожен раз."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Пароль (ще раз)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Поточний Пароль"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Новий Пароль"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Новий Пароль (ще раз)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Код"

#: account/models.py:26
msgid "user"
msgstr "користувач"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "адреса електронної пошти"

#: account/models.py:34
msgid "verified"
msgstr "підтверджено"

#: account/models.py:35
msgid "primary"
msgstr "основна"

#: account/models.py:41
msgid "email addresses"
msgstr "адреси електронної пошти"

#: account/models.py:151
msgid "created"
msgstr "створено"

#: account/models.py:152
msgid "sent"
msgstr "відправлено"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "ключ"

#: account/models.py:158
msgid "email confirmation"
msgstr "підтвердження електронної пошти"

#: account/models.py:159
msgid "email confirmations"
msgstr "підтвердження електронної пошти"

#: headless/apps.py:7
msgid "Headless"
msgstr "Без візуального інтерфейсу"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Ви не можете додати адресу електронної пошти до облікового запису, який "
"захищений двофакторною аутентифікацією."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Ви не можете вимкнути двофакторну аутентифікацію."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Ви не можете створити коди відновлення без увімкненої двофакторної "
"аутентифікації."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Ви не можете увімкнути двофакторну аутентифікацію, поки не підтвердите свою "
"адресу електронної пошти."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Головний ключ"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Резервний ключ"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Ключ № {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "Багатофакторна аутентифікація"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Коди для відновлення"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "Аутентифікатор TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Код аутентифікатора"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Без пароля"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Увімкнення режиму без пароля дозволяє вам увійти, використовуючи лише цей "
"ключ, але передбачає додаткові вимоги, такі як біометричні дані або захист "
"PIN-кодом."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Обліковий запис вже існує з цією адресою електронної пошти. Будь ласка, "
"спочатку увійдіть в цей обліковий запис, а потім підключіть ваш обліковий "
"запис %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Недійсний токен."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "У вашому обліковому записі не встановлено пароль."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr ""
"У вашому обліковому записі не підтверджено жодної адреси електронної пошти."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"Ви не можете відключити ваш останній обліковий запис стороннього "
"постачальника."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr ""
"Обліковий запис стороннього постачальника вже підключений до іншого "
"облікового запису."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Облікові Записи Соціальних Мереж"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "постачальник"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ідентифікатор постачальника"

#: socialaccount/models.py:56
msgid "name"
msgstr "ім'я"

#: socialaccount/models.py:58
msgid "client id"
msgstr "ідентифікатор клієнта"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "Ідентифікатор додатка або ключ споживача"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "ключ"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API ключ, клієнтський ключ або ключ споживача"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Ключ"

#: socialaccount/models.py:81
msgid "social application"
msgstr "додаток соціальної мережі"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "додатки соціальної мережі"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "останній вхід"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "дата приєднання"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "додаткові дані"

#: socialaccount/models.py:125
msgid "social account"
msgstr "обліковий запис соціальної мережі"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "облікові записи соціальних мереж"

#: socialaccount/models.py:160
msgid "token"
msgstr "токен"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) або токен доступу (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "ключ токена"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) або токен оновлення (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "діє до"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "токен додатку соціальної мережі"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "токени додатків соціальної мережі"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Недійсні дані профілю"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Увійти"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Скасувати"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Недійсна відповідь при отриманні токена запиту від \"%s\". Відповідь була: "
"%s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Недійсна відповідь під час отримання токена доступу від \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Не збережено токен запиту для \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Не збережено токен доступу для \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Немає доступу до приватних ресурсів за \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Недійсна відповідь при отриманні токена запиту від \"%s\""

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Обліковий Запис Неактивний"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Цей обліковий запис неактивний."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Ми відправили код на %(recipient)s. Код скоро застаріє, тому будь ласка, "
"введіть його якнайшвидше."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Підтвердити"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Запросити Код"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Підтвердіть Доступ"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr ""
"Будь ласка, проведіть повторну аутентифікацію для захисту вашого облікового "
"запису."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Альтернативні варіанти"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Підтвердження електронної пошти"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Введіть код з підтвердження електронної пошти:"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "адреса електронної пошти"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Увійти"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Введіть Код Для Входу."

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Відновлення Пароля"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Введіть код відновлення паролю"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Підтвердження номеру телефона"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Введіть код підтвердження номеру телефона:"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Адреси електронної пошти"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Ваш обліковий запис пов'язано з наступними адресами електронної пошти:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Підтверджено"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Непідтверджено"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Основний"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Зробити Основним"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Надіслати Повторне Підтвердження"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Видалити"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Додати Адресу Електронної Пошти"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Додати Електронну Пошту"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Ви дійсно хочете видалити обрану адресу електронної пошти?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Ви отримали цей лист, оскільки ви або хтось інший намагались зареєструвати "
"обліковий запис за допомогою адреси електронної пошти:\n"
"\n"
"%(email)s\n"
"\n"
"Однак вже існує обліковий запис з цією адресою електронної пошти. Якщо ви "
"забули про це, будь ласка, скористайтеся процедурою відновлення паролю, щоб "
"відновити свій обліковий запис:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Обліковий Запис Вже Існує"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Привіт від %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Дякуємо за використання %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Ви отримуєте цей лист, оскільки була зроблена наступна зміна у вашому "
"обліковому записі:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Якщо ви не впізнаєте цю зміну, будь ласка, негайно прийміть відповідні "
"заходи безпеки. Зміна у вашому обліковому записі походить з:\n"
"\n"
"- IP-адреса: %(ip)s\n"
"- Браузер: %(user_agent)s\n"
"- Дата: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Ваша електронна пошта була змінена з %(from_email)s на %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Електронна Пошта Змінена"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Ваша електронна пошта підтверджена."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Підтвердження Електронної Пошти"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Ви отримали цей лист, оскільки користувач %(user_display)s вказав вашу "
"електронну пошту для реєстрації облікового запису на %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Нижче наведено код підтвердження електронної пошти. Введіть його у "
"відкритому вікні браузера."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Для підтвердження реєстрації перейдіть за наступним посиланням: "
"%(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Будь Ласка, Підтвердіть Вашу Адресу Електронної Пошти"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""
"Адресу електронної пошти %(deleted_email)s було видалено з вашого облікового "
"запису."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Електронна Пошта Видалена"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Нижче наведений код для входу. Будь ласка, введіть його у відкритому вікні "
"браузера."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "Цей лист можна безпечно ігнорувати, якщо ви не ініціювали цю дію."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Код Для Входу"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Ваш пароль було змінено."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Пароль Змінено"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Нижче наведений код відновлення паролю. Введіть його у відкритому вікні "
"браузера."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Код відновлення паролю"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Ви отримали цей лист, оскільки ви або хтось інший запросив відновлення "
"пароля для вашого облікового запису.\n"
"\n"
"Цей лист можна безпечно ігнорувати, якщо ви не запитували відновлення "
"пароля. Клацніть по посиланню нижче, щоб відновити пароль."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Якщо ви забули, ваше ім'я користувача - %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Лист Для Відновлення Пароля"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Ваш пароль було відновленно."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Ваш пароль було встановлено."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Встановити Пароль"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Ви отримали цей лист, оскільки ви або хтось інший спробували отримати доступ "
"до облікового запису з електронною поштою %(email)s. Проте у нашій базі "
"даних немає жодного запису такого облікового запису."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Якщо це були ви, ви можете зареєструватися для облікового запису за "
"допомогою посилання нижче."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Невідомий Обліковий Запис"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Адреса Електронної Пошти"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Поточна електронна пошта"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Зміна на"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Ваша адреса електронної пошти ще не підтверджена."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Відмінити Зміну"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Зміна на"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Змінити Електронну Пошту"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Підтвердити Адресу Електронної Пошти"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Будь ласка, підтвердіть, що <a href=\"mailto:%(email)s\">%(email)s</a> є "
"адресоюелектронної пошти для користувача %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Не вдалося підтвердити %(email)s, оскільки вона вже підтверджена іншим "
"обліковим записом."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Термін дії посилання для підтвердження електронної пошти закінчився або воно "
"є недійсним. Будь ласка, <a href=\"%(email_url)s\">відправте новий запит на "
"підтвердження електронної пошти</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Якщо ви ще не створили обліковий запис, будь ласка, спочатку "
"%(link)sзареєструйтесь%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Увійти за допомогою пароль-ключа"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "Надішліть мені код для входу"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Вийти"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Ви впевнені, що хочете вийти?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr ""
"Ви не можете видалити свою основну адресу електронної пошти (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Лист-підтвердження надіслано на %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Ви підтвердили %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Видалено адресу електронної пошти %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Успішно увійшли як %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Ви вийшли."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Код для входу було надіслано на %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Пароль успішно змінено."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Пароль успішно встановлено."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Код для входу було надіслано на %(recipient)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Номер телефону %(phone)s підтверджено."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Основна адреса електронної пошти встановлена."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Змінити Пароль"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Забули Пароль?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Забули пароль? Введіть свою адресу електронної пошти нижче, і ми надішлемо "
"вам листа з інструкціями для його відновлення."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Відновити Мій Пароль"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Будь ласка, зв'яжіться з нами, якщо у вас виникли проблеми зі відновлення "
"пароля."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Ми відправили вам електронного листа. Якщо ви його не отримали, перевірте "
"папку «Спам». В іншому випадку, зверніться до нас, якщо ви не отримаєте його "
"протягом кількох хвилин."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Недійсний Токен"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Посилання для відновлення пароля недійсне, можливо, воно вже було "
"використане. Будь ласка, запросіть <a href=\"%(passwd_reset_url)s\">нове "
"відновлення пароля</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Ваш пароль було змінено."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Встановити Пароль"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Змінити номер телефону"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Поточний номер телефону"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Ваш номер телефону ще не підтверджено."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Введіть ваш пароль:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Ви отримаєте особливий код для входу без пароля."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Запросити Код"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Інші варіанти входу"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Реєстрація"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Зареєструватись"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr ""
"Вже маєте обліковий запис? Тоді будь ласка, %(link)sувійти%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Зареєструватися за допомогою ключа доступу"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Реєстрація за допомогою ключа доступу"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Інші варіанти"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Реєстрація Закрита"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Вибачте, але реєстрація наразі закрита."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Примітка"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Ви вже увійшли як %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Попередження:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"На даний момент у вас немає встановленої жодної адреси електронної пошти. "
"Рекомендується додати адресу електронної пошти для отримання сповіщень, "
"відновлення паролю та інших операцій."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Підтвердіть Вашу Адресу Електронної Пошти"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Ми відправили вам лист для підтвердження. Слідуйте по посиланню, щоб "
"завершити процес реєстрації. Якщо ви не знайшли листа в основній скринці, "
"перевірте папку «Спам». Будь ласка, зв'яжіться з нами, якщо ви не отримаєте "
"листа з підтвердженням протягом кількох хвилин."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Ця частина сайту вимагає підтвердження того, що\n"
"ви той, за кого себе видаєте. З цією метою ми потребуємо\n"
"підтвердити власність вашої адреси електронної пошти. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Ми відправили вам лист для\n"
"підтвердження. Будь ласка, перейдіть за посиланням у цьому листі. Якщо ви не "
"знайшли листа з підтвердженням у головній скринці, перевірте папку «Спам». "
"Інакше\n"
"зв'яжіться з нами, якщо ви не отримаєте його протягом кількох хвилин."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Примітка:</strong> ви все ще можете <a "
"href=\"%(email_url)s\">змінити свою адресу електронної пошти</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Повідомлення:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Меню:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Підключені Облікові Записи"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Двофакторна аутентифікація"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Сесії"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Ваш обліковий запис захищений двофакторною аутентифікацією. Будь ласка, "
"введіть код автентифікатора:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Згенеровано новий набір кодів для відновлення двофакторної аутентифікації."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Створено Нові Коди Для Відновлення Доступу"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Аутентифікаційний додаток увімкнено."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Аутентифікаційний Додаток Увімкнено"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Аутентифікаційний додаток вимкнуто."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Аутентифікаційний Додаток Вимкнуто"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Додано новий ключ безпеки."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Додано Ключ Безпеки"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Ключ безпеки було видалено."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Ключ Безпеки Видалено"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Додаток Для Аутентифікації"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Аутентифікація за допомогою додатка для аутентифікації увімкнена."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Додаток для аутентифікації не увімкнений."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Вимкнути"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Увімкнути"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Ключі Безпеки"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Ви додали %(count)s захисний ключ."
msgstr[1] "Ви додали %(count)s захисних ключів."
msgstr[2] "Ви додали %(count)s захисних ключів."
msgstr[3] "Ви додали %(count)s захисних ключів."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Не було додано жодних ключів безпеки."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Керувати"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Додати"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Коди Для Відновлення"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] "Доступно %(unused_count)s з %(total_count)s кодів відновлення."
msgstr[1] "Доступно %(unused_count)s з %(total_count)s кодів відновлення."
msgstr[2] "Доступно %(unused_count)s з %(total_count)s кодів відновлення."
msgstr[3] "Доступно %(unused_count)s з %(total_count)s кодів відновлення."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Коди для відновлення не налаштовано."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Перегляд"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Завантажити"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Згенерувати"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Згенеровано новий набір кодів для відновлення."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Додано ключ безпеки."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Ключ безпеки видалено."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Введіть код з аутентифікаційного додатка:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Ви збираєтесь згенерувати новий набір кодів для відновлення вашого "
"облікового запису."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Ця дія зробить недійсними ваші поточні коди."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Ви впевнені?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Не використані коди"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Завантажити коди"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Згенерувати нові коди"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Увімкнути Додаток Для Аутентифікації"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Для захисту вашого облікового запису двофакторною аутентифікацією "
"відскануйте QR-код нижче за допомогою вашого додатка для аутентифікації. "
"Потім введіть згенерований додатком код підтвердження нижче."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Ключ аутентифікатора"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Ви можете зберегти цей ключ і використовувати його для перевстановлення "
"вашого додатка для аутентифікації в майбутньому."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Вимкнути Додаток Для Аутентифікації"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Ви збираєтесь вимкнути аутентифікацію на основі додатку для аутентифікації. "
"Ви впевнені?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr "Довіряти цьому браузеру?"

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr "Довіряти протягом %(period)s"

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr "Не довіряти"

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Додати Ключ Безпеки"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Видалити Ключ Безпеки"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Ви впевнені, що хочете видалити цей ключ безпеки?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Використання"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "пароль-ключ"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Ключ безпеки"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Цей ключ не вказує, чи є він пароль-ключем."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Невизначений"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Додано %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Останнє використання %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Редагувати"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Редагувати Ключ Безпеки"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Зберегти"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Створити ключ доступу"

#: templates/mfa/webauthn/signup_form.html:10
#, fuzzy
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Ви створюєте ключ доступу до вашого облікового запису. Оскільки можна "
"створити більше за один ключ доступу, ви можете обрати назву для "
"ідентифікації кожного з них."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Створити"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Ця функція потребує JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Невдалий Вхід Через Сторонній Сервіс"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Під час спроби входу через ваш обліковий запис стороннього сервісу сталася "
"помилка."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Ви можете увійти до свого облікового запису, використовуючи будь-який з "
"наступних облікових записів сторонніх сервісів:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr ""
"Наразі до цього облікового запису не підключено жодних облікових записів "
"сторонніх сервісів."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Додати Обліковий Запис Стороннього Сервісу"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""
"Обліковий запис від %(provider)s було підключено до вашого облікового запису."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Обліковий Запис Стороннього Сервісу Підключено"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"Обліковий запис від %(provider)s було відключено від вашого облікового "
"запису."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Обліковий Запис Стороннього Сервісу Відключено"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Підключити %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Ви збираєтеся підключити новий обліковий запис стороннього сервісу "
"%(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Увійти через %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Ви збираєтеся увійти, використовуючи обліковий запис стороннього сервісу "
"%(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Продовжити"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Вхід Скасовано"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Ви вирішили скасувати вхід на наш сайт, використовуючи один із ваших "
"існуючих облікових записів. Якщо це була помилка, будь ласка, перейдіть до "
"<a href=\"%(login_url)s\">входу</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Обліковий запис стороннього сервісу було підключено."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Обліковий запис стороннього сервісу було відключено."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Ви збираєтеся увійти за допомогою облікового запису %(provider_name)s на\n"
" сайт %(site_name)s. Як завершальний крок, будь ласка, заповніть наступну "
"форму:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Або скористайтеся стороннім сервісом"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Усі інші сеанси завершено."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Розпочато о"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP Адреса"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Браузер"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Останній вхід о"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Поточний"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Вийти З Інших Сеансів"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Сеанси Користувача"

#: usersessions/models.py:92
msgid "session key"
msgstr "ключ сеансу"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Підключені Облікові Записи"
