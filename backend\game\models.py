from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.utils.translation import gettext_lazy as _

import gzip, json
from io import BytesIO
from django.conf import settings

# ───────────────────────────────────────────────────────────
#  Core rule‑book entities (static data)
#  These rows are created via fixtures / Django admin and very rarely change at runtime.
# ───────────────────────────────────────────────────────────

class Attribute(models.Model):
    """STR, DEX, TEC …
    Kept minimal – per‑character values & XP live in SaveGame json, not here.
    """

    code = models.Char<PERSON>ield(max_length=8, primary_key=True)  # e.g. "STR" / "DEX"
    name = models.Char<PERSON><PERSON>(max_length=60)

    class Meta:
        verbose_name = _("attribute")
        verbose_name_plural = _("attributes")

    def __str__(self):
        return self.code


class Subskill(models.Model):
    code = models.Cha<PERSON><PERSON><PERSON>(max_length=32, primary_key=True)  # e.g. "melee"
    name = models.CharField(max_length=60)
    primary_attr = models.ForeignKey(Attribute, on_delete=models.PROTECT, related_name="subskills")

    class Meta:
        verbose_name = _("sub‑skill")
        verbose_name_plural = _("sub‑skills")

    def __str__(self):
        return self.code


class Perk(models.Model):
    """
    Enhanced perk system with multiple effect types.
    """
    # Perk effect types
    EFFECT_TYPES = [
        ('roll_bonus', 'Bonus do rzutów'),           # +X% do testów z tagami
        ('attr_growth', 'Szybszy rozwój atrybutów'), # +X% szansy na XP atrybutów
        ('skill_bonus', 'Łatwiejsze testy'),         # -X trudności testów z tagami
        ('main_xp_bonus', 'Szybszy rozwój postaci'), # +X% main XP
        ('unlock_actions', 'Nowe akcje'),            # Odblokowuje akcje z tagami
        ('special', 'Efekt specjalny'),              # Unikalne efekty
    ]

    id = models.CharField(max_length=64, primary_key=True)
    name = models.CharField(max_length=80)
    description = models.TextField()
    level_min = models.PositiveSmallIntegerField()
    level_max = models.PositiveSmallIntegerField()
    tags = ArrayField(models.CharField(max_length=32), blank=True, default=list)

    # Enhanced perk system fields
    effect_type = models.CharField(
        max_length=20,
        choices=EFFECT_TYPES,
        default='roll_bonus',
        help_text="Typ efektu perka"
    )
    effect_value = models.IntegerField(
        default=0,
        help_text="Wartość efektu (np. 10 dla +10%)"
    )
    effect_tags = ArrayField(
        models.CharField(max_length=32),
        blank=True,
        default=list,
        help_text="Tagi na które wpływa efekt"
    )

    # Special effects configuration
    special_config = models.JSONField(
        default=dict,
        blank=True,
        help_text="Konfiguracja specjalnych efektów"
    )

    class Meta:
        verbose_name = _("perk")
        verbose_name_plural = _("perks")

    def __str__(self):
        return self.name

    def get_effect_description(self):
        """Generate effect description based on type and value."""
        if self.effect_type == 'roll_bonus':
            return f"+{self.effect_value}% do testów z tagami: {', '.join(self.effect_tags)}"
        elif self.effect_type == 'attr_growth':
            return f"+{self.effect_value}% szansy na XP atrybutów: {', '.join(self.effect_tags)}"
        elif self.effect_type == 'skill_bonus':
            return f"-{self.effect_value} trudności testów z tagami: {', '.join(self.effect_tags)}"
        elif self.effect_type == 'main_xp_bonus':
            return f"+{self.effect_value}% main XP"
        elif self.effect_type == 'unlock_actions':
            return f"Odblokowuje akcje z tagami: {', '.join(self.effect_tags)}"
        else:
            return self.description


# ───────────────────────────────────────────────────────────
#  Game‑world structure
# ───────────────────────────────────────────────────────────

class Chapter(models.Model):
    """Rozdział gry - główna jednostka organizacyjna."""

    title = models.CharField(max_length=120)
    description = models.TextField(blank=True)
    order = models.PositiveIntegerField(default=1, help_text="Kolejność rozdziału w grze")
    is_active = models.BooleanField(default=True, help_text="Czy rozdział jest aktywny")

    class Meta:
        ordering = ['order']
        verbose_name = _("chapter")
        verbose_name_plural = _("chapters")

    def __str__(self):
        return f"Rozdział {self.order}: {self.title}"


class Subchapter(models.Model):
    """Podrozdział - grupa scen w ramach rozdziału."""

    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, related_name="subchapters")
    title = models.CharField(max_length=120)
    description = models.TextField(blank=True)
    order = models.PositiveIntegerField(default=1, help_text="Kolejność podrozdziału w rozdziale")
    is_active = models.BooleanField(default=True, help_text="Czy podrozdział jest aktywny")

    class Meta:
        ordering = ['chapter__order', 'order']
        unique_together = ['chapter', 'order']
        verbose_name = _("subchapter")
        verbose_name_plural = _("subchapters")

    def __str__(self):
        return f"{self.chapter.order}.{self.order}: {self.title}"


class GameEnd(models.Model):
    """Zakończenie gry - może być końcem gry, rozdziału lub podrozdziału."""

    class EndTypeChoices(models.TextChoices):
        GAME = 'game', _('Koniec Gry')
        CHAPTER = 'chapter', _('Koniec Rozdziału')
        SUBCHAPTER = 'subchapter', _('Koniec Podrozdziału')

    title = models.CharField(max_length=120)
    end_type = models.CharField(
        max_length=20,
        choices=EndTypeChoices.choices,
        default=EndTypeChoices.GAME,
        help_text=_("Typ zakończenia")
    )
    message = models.TextField(
        blank=True,
        help_text=_("Wiadomość wyświetlana graczowi przy zakończeniu")
    )
    is_active = models.BooleanField(default=True, help_text="Czy zakończenie jest aktywne")

    class Meta:
        verbose_name = _("game end")
        verbose_name_plural = _("game ends")
        ordering = ['end_type', 'title']

    def __str__(self):
        return f"{self.get_end_type_display()}: {self.title}"


class Scene(models.Model):
    class MoodChoices(models.TextChoices):
        NEUTRAL = 'neutral', _('Neutralny')
        POSITIVE = 'positive', _('Pozytywny')
        DANGEROUS = 'dangerous', _('Niebezpieczny')
        PEACEFUL = 'peaceful', _('Spokojny')
        MYSTERIOUS = 'mysterious', _('Tajemniczy')
        MELANCHOLIC = 'melancholic', _('Melancholijny')
        TENSE = 'tense', _('Napięcie')
        ENERGETIC = 'energetic', _('Energiczny')
        ROMANTIC = 'romantic', _('Romantyczny')
        EPIC = 'epic', _('Epicki')

    subchapter = models.ForeignKey(
        Subchapter,
        on_delete=models.CASCADE,
        related_name="scenes",
        null=True,
        blank=True,
        help_text="Podrozdział do którego należy scena"
    )
    slug = models.SlugField(unique=True)
    title = models.CharField(max_length=120)
    description = models.TextField(blank=True)
    mood = models.CharField(
        max_length=20,
        choices=MoodChoices.choices,
        default=MoodChoices.NEUTRAL,
        help_text=_("Nastrój sceny wpływający na tło i atmosferę")
    )

    # Available actions for this scene
    available_actions = models.JSONField(
        default=list,
        blank=True,
        help_text="Lista ID akcji dostępnych w tej scenie (np. ['zombie_run_away', 'zombie_talk'])"
    )

    diagram_json = models.JSONField(default=dict, blank=True)  # full react‑flow JSON
    entry_conditions = models.JSONField(default=dict, blank=True)
    exit_effects = models.JSONField(default=dict, blank=True)

    def get_available_actions(self):
        """Zwraca QuerySet akcji dostępnych w tej scenie."""
        if not self.available_actions:
            return ActionMeta.objects.none()
        return ActionMeta.objects.filter(id__in=self.available_actions)

    def __str__(self):
        return self.title


class GameObject(models.Model):
    class Type(models.TextChoices):
        ITEM = "item", _("Item")
        NPC = "npc", _("NPC")
        HOTSPOT = "spot", _("Hot‑spot")

    scene = models.ForeignKey(Scene, on_delete=models.CASCADE, related_name="game_objects")
    type = models.CharField(max_length=4, choices=Type.choices)
    name = models.CharField(max_length=120)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"


class TransitionType(models.TextChoices):
    """Typy przejść w grze."""
    SCENE = "scene", _("Przejście do sceny")
    CHAPTER = "chapter", _("Przejście do rozdziału")
    SUBCHAPTER = "subchapter", _("Przejście do podrozdziału")


class Transition(models.Model):
    """Directed edge on the story graph."""

    origin = models.ForeignKey(Scene, on_delete=models.CASCADE, related_name="outgoing")

    # Typ przejścia
    transition_type = models.CharField(
        max_length=20,
        choices=TransitionType.choices,
        default=TransitionType.SCENE,
        help_text="Typ przejścia - do sceny, rozdziału lub podrozdziału"
    )

    # Cele przejścia (tylko jedno z poniższych powinno być wypełnione)
    target = models.ForeignKey(
        Scene,
        on_delete=models.CASCADE,
        related_name="incoming",
        null=True,
        blank=True,
        help_text="Scena docelowa (dla transition_type='scene')"
    )
    target_chapter = models.ForeignKey(
        Chapter,
        on_delete=models.CASCADE,
        related_name="incoming_transitions",
        null=True,
        blank=True,
        help_text="Rozdział docelowy (dla transition_type='chapter')"
    )
    target_subchapter = models.ForeignKey(
        Subchapter,
        on_delete=models.CASCADE,
        related_name="incoming_transitions",
        null=True,
        blank=True,
        help_text="Podrozdział docelowy (dla transition_type='subchapter')"
    )

    condition_expr = models.CharField(max_length=255, help_text="Boolean‑like expression, e.g. 'DEX > 2 && flags.door_unlocked' ")
    description = models.CharField(max_length=255, blank=True, help_text="Label shown to the player")
    auto = models.BooleanField(default=False, help_text="If True, the resolver will take this transition immediately once "
                  "condition_expr evaluates to True (no player choice needed).",
    )

    class Meta:
        verbose_name = _("transition")
        verbose_name_plural = _("transitions")

    def clean(self):
        """Walidacja - tylko jeden cel może być ustawiony."""
        from django.core.exceptions import ValidationError

        targets = [self.target, self.target_chapter, self.target_subchapter]
        filled_targets = [t for t in targets if t is not None]

        if len(filled_targets) != 1:
            raise ValidationError("Dokładnie jeden cel przejścia musi być ustawiony.")

        # Sprawdź zgodność typu z celem
        if self.transition_type == TransitionType.SCENE and not self.target:
            raise ValidationError("Dla typu 'scene' wymagana jest scena docelowa.")
        elif self.transition_type == TransitionType.CHAPTER and not self.target_chapter:
            raise ValidationError("Dla typu 'chapter' wymagany jest rozdział docelowy.")
        elif self.transition_type == TransitionType.SUBCHAPTER and not self.target_subchapter:
            raise ValidationError("Dla typu 'subchapter' wymagany jest podrozdział docelowy.")

    def get_target_display(self):
        """Zwraca czytelny opis celu przejścia."""
        if self.transition_type == TransitionType.SCENE and self.target:
            return f"Scena: {self.target.title}"
        elif self.transition_type == TransitionType.CHAPTER and self.target_chapter:
            return f"Rozdział: {self.target_chapter.title}"
        elif self.transition_type == TransitionType.SUBCHAPTER and self.target_subchapter:
            return f"Podrozdział: {self.target_subchapter.title}"
        return "Nieznany cel"

    def __str__(self):
        return f"{self.origin.slug} → {self.get_target_display()}"


# ───────────────────────────────────────────────────────────
#  Action definitions + logs
# ───────────────────────────────────────────────────────────

class ActionMeta(models.Model):
    class ActionType(models.TextChoices):
        SKILL_TEST = 'skill_test', _('Test umiejętności')
        SCENE_TRANSITION = 'scene_transition', _('Przejście do sceny')
        DIALOGUE_CONTINUE = 'dialogue_continue', _('Kontynuacja dialogu')
        STORY_CHOICE = 'story_choice', _('Wybór fabularny')

    id = models.CharField(max_length=64, primary_key=True)
    label = models.CharField(max_length=120)
    description = models.TextField(blank=True)

    action_type = models.CharField(
        max_length=20,
        choices=ActionType.choices,
        default=ActionType.SKILL_TEST,
        help_text="Typ akcji - test umiejętności lub przejście/wybór fabularny"
    )

    # Fields for skill tests
    base_diff = models.PositiveSmallIntegerField(null=True, blank=True)
    attribute = models.ForeignKey(Attribute, on_delete=models.PROTECT, null=True, blank=True)

    # Fields for scene transitions
    target_scene = models.CharField(
        max_length=100,
        blank=True,
        help_text="Slug sceny docelowej dla przejść (tylko dla action_type='scene_transition')"
    )

    # Conditional scene transitions based on action outcome
    success_scene = models.CharField(
        max_length=100,
        blank=True,
        help_text="Scena docelowa przy sukcesie/krytycznym sukcesie (dla skill_test)"
    )
    failure_scene = models.CharField(
        max_length=100,
        blank=True,
        help_text="Scena docelowa przy porażce/częściowej porażce (dla skill_test)"
    )

    # Legacy single skill (for backward compatibility)
    subskill = models.ForeignKey(Subskill, on_delete=models.PROTECT, null=True, blank=True)

    # New multi-skill system
    available_skills = models.JSONField(
        default=list,
        blank=True,
        help_text="Lista kodów umiejętności dostępnych dla tej akcji (np. ['firearms', 'precision_shooting'])"
    )
    primary_skill = models.CharField(
        max_length=50,
        blank=True,
        help_text="Kod głównej umiejętności (auto-wybierana jeśli gracz nie wybierze)"
    )
    allow_skill_choice = models.BooleanField(
        default=True,
        help_text="Czy gracz może wybierać umiejętność, czy używa tylko primary_skill"
    )

    perk_tags = ArrayField(models.CharField(max_length=32), default=list, blank=True)

    # growth tables, ex.: {"DEX":50, "STR":30}
    attr_growth = models.JSONField(default=dict, blank=True)
    skill_growth = models.JSONField(default=dict, blank=True)

    # ✅ Pozycja w edytorze scen
    diagram_json = models.JSONField(
        default=dict,
        blank=True,
        help_text="Dane diagramu (pozycja węzła w edytorze scen)"
    )

    def get_available_skills_for_player(self, player_stats):
        """
        Zwraca listę umiejętności dostępnych dla gracza w tej akcji.
        Filtruje tylko te umiejętności, które gracz faktycznie posiada.
        """
        if not self.available_skills:
            # Fallback to legacy single skill
            if self.subskill:
                skill_code = self.subskill.code
                if skill_code in player_stats.get('skills', {}):
                    return [skill_code]
            return []

        # Filter available skills by what player actually has
        player_skills = player_stats.get('skills', {})
        return [skill for skill in self.available_skills if skill in player_skills]

    def get_primary_skill(self):
        """Zwraca kod głównej umiejętności dla tej akcji."""
        if self.primary_skill:
            return self.primary_skill
        elif self.subskill:
            return self.subskill.code
        elif self.available_skills:
            return self.available_skills[0]
        return None

    def __str__(self):
        return self.label


class ActionLog(models.Model):
    action = models.ForeignKey(ActionMeta, on_delete=models.CASCADE)
    scene = models.ForeignKey(Scene, on_delete=models.CASCADE)
    outcome = models.CharField(max_length=20)  # fail / partial / success / critical
    roll_value = models.PositiveSmallIntegerField()
    target_value = models.PositiveSmallIntegerField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-timestamp"]
        verbose_name = _("action log entry")
        verbose_name_plural = _("action logs")

    def __str__(self):
        return f"{self.action_id} [{self.outcome}] @ {self.timestamp:%Y‑%m‑%d %H:%M}"

# ───────────────────────────────────────────────────────────
#  Save game
# ───────────────────────────────────────────────────────────

class SaveGame(models.Model):
    """Stan gry spakowany gzipem (wiele slotów na użytkownika)."""
    user        = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    slot       = models.PositiveSmallIntegerField(default=0)
    data_gzip   = models.BinaryField()
    updated_at  = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("user", "slot")

    # helpery ---------------------------------------------------
    @staticmethod
    def _pack(data: dict) -> bytes:
        buff = BytesIO()
        with gzip.GzipFile(fileobj=buff, mode="wb") as gz:
            gz.write(json.dumps(data).encode())
        return buff.getvalue()

    @staticmethod
    def _unpack(blob: bytes) -> dict:
        with gzip.GzipFile(fileobj=BytesIO(blob), mode="rb") as gz:
            return json.loads(gz.read().decode())

    # wysoki poziom API ----------------------------------------
    @classmethod
    def save_for(cls, user, data: dict, slot: int = 0):
        obj, _ = cls.objects.get_or_create(user=user, slot=slot)
        obj.data_gzip = cls._pack(data)
        obj.save()
        return obj

    @classmethod
    def load_for(cls, user, slot: int = 0) -> dict | None:
        try:
            blob = cls.objects.only("data_gzip").get(user=user, slot=slot).data_gzip
            return cls._unpack(blob)
        except cls.DoesNotExist:
            return None

    @classmethod        
    def latest_for(cls, user, *, slot: int = 0):
        """
        Zwróć ostatni zapis danego gracza w podanym slocie (None gdy brak).
        """
        try:
            return cls.objects.get(user=user, slot=slot)
        except cls.DoesNotExist:
            return None

    def __str__(self):
        return f"SaveGame<{self.user}>"