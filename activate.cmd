@echo off
echo Activating Python virtual environment...
cd /d "F:\Grimveil\rpg-project"
call venv\Scripts\activate.bat
echo.
echo Virtual environment activated!
echo Python version:
python --version
echo.
echo Pip version:
pip --version
echo.
echo You can now run Django commands like:
echo   python backend\manage.py runserver
echo   python backend\manage.py migrate  
echo   python backend\manage.py test
echo.
cmd /k
