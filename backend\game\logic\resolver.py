"""
game.logic.resolver
~~~~~~~~~~~~~~~~~~~

`resolve_transitions(scene_slug, player, *, ctx_extra=None)` returns **a list of
Transition instances** that the player can currently take.

Rules applied:

1. Only `Transition.objects.filter(origin__slug=scene_slug)` are considered.
2. A transition is *eligible* when **any** is true:
   • `auto is True`  →  always available (used for autosaves/tutorial flows)  
   • `condition_expr` is empty / null  
   • `eval_expr(condition_expr, build_context(player, …))` is **truthy**
3. The queryset is evaluated only once → resulting list is detached from ORM
   (safe for further `.delete()` / `.update()` operations).

The helper is intentionally thin – perfect for unit testing *and* for plugging
into views (e.g. `PlayResolveView`).
"""

from __future__ import annotations

from typing import List

from django.db.models import Q

from game.models import Transition
from game.logic.expr import eval_expr
from game.logic.context import build_context


__all__ = ["resolve_transitions"]


def _satisfied(tr: Transition, ctx: dict) -> bool:
    """Return **True** when transition's expression passes in the given ctx."""
    if tr.auto or not (tr.condition_expr or "").strip():
        return True
    try:
        return bool(eval_expr(tr.condition_expr, ctx))
    except Exception:  # pragma: no cover
        # Fail-safe: if expression explodes we treat transition as *blocked*.
        return False


# ---------------------------------------------------------------------------


def resolve_transitions(
    scene_slug: str,
    player,
    *,
    ctx_extra: dict | None = None,
) -> List[Transition]:
    """
    Return all transitions the player can currently use from *scene_slug*.

    The caller may supply `ctx_extra` to inject temporary flags
    (e.g. `{ "has_key": True }` during a quest step).
    """
    # build eval namespace only once
    ctx = build_context(player, scene=scene_slug, extra=ctx_extra or {})

    qs = Transition.objects.filter(origin__slug=scene_slug).select_related(
        "origin", "target", "target_chapter", "target_subchapter"
    )

    eligible: list[Transition] = [tr for tr in qs if _satisfied(tr, ctx)]
    return eligible.copy()  # detach from queryset (tests mutate sometimes)
