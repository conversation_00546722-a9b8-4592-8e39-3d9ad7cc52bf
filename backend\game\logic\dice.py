"""
game.logic.dice
~~~~~~~~~~~~~~~

Dice rolling mechanics ported from main.js
- Ceiling rule (max 8 dice pool, rest as flat bonus)
- Difficulty thresholds (1-8)
- Perk bonuses (5% = +1 flat bonus)
- Outcome calculation (fail, partial_fail, success, critical)
"""

import random
from typing import Dict, Any, List
from django.contrib.auth import get_user_model

from ..models import Action<PERSON><PERSON>, Perk, SaveGame

User = get_user_model()

# Difficulty thresholds (1-8)
DIFFICULTY_THRESHOLDS = [0, 4, 6, 8, 10, 12, 14, 16, 18]

# Outcome multipliers for XP
OUTCOME_MULTIPLIERS = {
    "fail": 0.5,
    "partial_fail": 0.75,
    "success": 1.0,
    "critical": 1.5,
}

# Main XP multiplier
MAIN_XP_MULTIPLIER = 5


def best_of(pool_size: int) -> int:
    """Roll pool_size d10s and return the highest result."""
    if pool_size <= 0:
        return 1  # Minimum roll
    
    rolls = [random.randint(1, 10) for _ in range(pool_size)]
    return max(rolls)


def get_player_attributes(user: User) -> Dict[str, int]:
    """Get player's current attribute values from SaveGame or defaults."""
    save_data = SaveGame.load_for(user, slot=0)
    if not save_data:
        # Default starting attributes
        return {
            "STR": 1, "DEX": 1, "TEC": 1, 
            "PER": 1, "WIL": 1, "CHA": 1
        }
    
    # Extract attributes from save data
    attributes = save_data.get("attributes", {})
    return {
        "STR": attributes.get("STR", {}).get("value", 1),
        "DEX": attributes.get("DEX", {}).get("value", 1),
        "TEC": attributes.get("TEC", {}).get("value", 1),
        "PER": attributes.get("PER", {}).get("value", 1),
        "WIL": attributes.get("WIL", {}).get("value", 1),
        "CHA": attributes.get("CHA", {}).get("value", 1),
    }


def get_player_subskills(user: User) -> Dict[str, int]:
    """Get player's current subskill values from SaveGame or defaults."""
    save_data = SaveGame.load_for(user, slot=0)
    if not save_data:
        # Default starting subskills - all new skills at 0
        return {
            # STR skills
            "melee": 0, "athletics": 0, "intimidation": 0, "demolitions": 0,
            # DEX skills
            "firearms": 0, "stealth": 0, "lockpicking": 0, "driving": 0,
            # TEC skills
            "hacking": 0, "electronics": 0, "crafting": 0, "repair": 0,
            # PER skills
            "observation": 0, "investigation": 0, "tracking": 0,
            # WIL skills
            "concentration": 0, "resistance": 0,
            # CHA skills
            "persuasion": 0, "deception": 0, "leadership": 0,
        }

    # Extract subskills from save data
    subskills = save_data.get("subskills", {})
    result = {}

    # Get all skills with fallback to 0
    all_skills = [
        "melee", "athletics", "intimidation", "demolitions",
        "firearms", "stealth", "lockpicking", "driving",
        "hacking", "electronics", "crafting", "repair",
        "observation", "investigation", "tracking",
        "concentration", "resistance",
        "persuasion", "deception", "leadership"
    ]

    for skill in all_skills:
        result[skill] = subskills.get(skill, {}).get("value", 0)

    return result


def get_player_perks(user: User) -> List[str]:
    """Get player's chosen perks from SaveGame."""
    save_data = SaveGame.load_for(user, slot=0)
    if not save_data:
        return []
    
    return save_data.get("chosen_perks", [])


def calculate_perk_effects(action_meta: ActionMeta, chosen_perks: List[str]) -> Dict[str, Any]:
    """
    Calculate all perk effects for an action.

    Returns:
        Dict with keys: roll_bonus, skill_bonus (difficulty reduction), special_effects, detailed_sources
    """
    effects = {
        "roll_bonus": 0,      # Flat bonus to roll result
        "skill_bonus": 0,     # Difficulty reduction
        "special_effects": [], # Special effects that apply
        "detailed_sources": {  # Detailed breakdown of sources
            "roll_bonus_sources": [],
            "skill_bonus_sources": []
        }
    }

    # Get all perk objects that the player has
    player_perk_objects = Perk.objects.filter(id__in=chosen_perks)

    for perk in player_perk_objects:
        # Check if perk's effect tags match action's perk tags
        perk_applies = any(tag in action_meta.perk_tags for tag in perk.effect_tags)

        if perk_applies:
            if perk.effect_type == 'roll_bonus':
                # Convert percentage to flat bonus (10% = +1)
                bonus_value = perk.effect_value // 10
                effects["roll_bonus"] += bonus_value
                effects["detailed_sources"]["roll_bonus_sources"].append({
                    "perk_name": perk.name,
                    "perk_id": perk.id,
                    "original_value": perk.effect_value,
                    "converted_bonus": bonus_value,
                    "description": f"{perk.name}: {perk.effect_value}% → +{bonus_value}"
                })

            elif perk.effect_type == 'skill_bonus':
                # Reduce difficulty by effect value
                effects["skill_bonus"] += perk.effect_value
                effects["detailed_sources"]["skill_bonus_sources"].append({
                    "perk_name": perk.name,
                    "perk_id": perk.id,
                    "bonus_value": perk.effect_value,
                    "description": f"{perk.name}: -{perk.effect_value} trudności"
                })

            elif perk.effect_type == 'special':
                # Add special effect
                effects["special_effects"].append({
                    "perk_id": perk.id,
                    "perk_name": perk.name,
                    "config": perk.special_config
                })

    return effects


def calculate_perk_bonus(action_meta: ActionMeta, chosen_perks: List[str]) -> int:
    """Legacy function for backward compatibility."""
    effects = calculate_perk_effects(action_meta, chosen_perks)
    return effects["roll_bonus"]


def action_test(action_meta: ActionMeta, user: User, situational_mod: int = 0, chosen_skill: str = None) -> Dict[str, Any]:
    """
    Perform action test with ceiling rule.

    Args:
        action_meta: ActionMeta instance
        user: User instance
        situational_mod: Situational modifier
        chosen_skill: Optional skill code chosen by player

    Returns:
        Dict with keys: outcome, R (roll result), T (target), details, available_skills
    """
    # Get player stats
    attributes = get_player_attributes(user)
    subskills = get_player_subskills(user)
    chosen_perks = get_player_perks(user)

    # Get attribute level
    if action_meta.attribute:
        attr_code = action_meta.attribute.code
        attr_level = attributes.get(attr_code, 0)
        print(f"🎯 Using attribute: {attr_code} (level: {attr_level})")
    else:
        # Fallback - use DEX as default for skill tests
        attr_code = 'DEX'
        attr_level = attributes.get(attr_code, 0)
        print(f"⚠️ ActionMeta {action_meta.id} has no attribute, using DEX fallback (level: {attr_level})")

    # Determine which skill to use
    player_stats = {"skills": subskills}
    available_skills = action_meta.get_available_skills_for_player(player_stats)

    if chosen_skill and chosen_skill in available_skills:
        # Player chose a specific skill
        subskill_code = chosen_skill
    elif available_skills:
        # Auto-choose primary skill or first available
        primary = action_meta.get_primary_skill()
        subskill_code = primary if primary in available_skills else available_skills[0]
    elif action_meta.subskill:
        # Fallback to legacy single skill
        subskill_code = action_meta.subskill.code
    else:
        # No skill available - use attribute only
        subskill_code = None

    skill_level = subskills.get(subskill_code, 0) if subskill_code else 0
    
    # 1) Calculate dice pool - ONLY from skill level (no attribute bonus)
    pool_size = 1 + skill_level  # Attributes no longer affect dice pool
    flat_bonus = 0

    # 2) Apply ceiling rule (max 8 dice)
    if pool_size > 8:
        flat_bonus += pool_size - 8
        pool_size = 8
    
    # 3) Calculate perk effects
    perk_effects = calculate_perk_effects(action_meta, chosen_perks)
    flat_bonus += perk_effects["roll_bonus"]

    # 4) Roll dice
    roll_result = best_of(pool_size)
    total_result = roll_result + flat_bonus + situational_mod

    # 5) Get target threshold with perk difficulty reduction
    base_threshold = DIFFICULTY_THRESHOLDS[action_meta.base_diff]
    target_threshold = max(1, base_threshold - perk_effects["skill_bonus"])  # Min threshold of 1
    
    # 6) Determine outcome
    if total_result < target_threshold - 2:
        outcome = "fail"
    elif total_result < target_threshold:
        outcome = "partial_fail"
    elif total_result < target_threshold + 3:
        outcome = "success"
    else:
        outcome = "critical"
    
    # Calculate detailed breakdown of flat_bonus sources
    flat_bonus_sources = []

    # Skill level bonus (if any)
    skill_bonus_from_level = skill_level
    if skill_bonus_from_level > 0:
        flat_bonus_sources.append({
            "source": "skill_level",
            "description": f"Poziom umiejętności: +{skill_bonus_from_level} kości",
            "value": skill_bonus_from_level
        })

    # Ceiling rule bonus (if any)
    ceiling_bonus = max(0, (1 + skill_level) - 8)
    if ceiling_bonus > 0:
        flat_bonus_sources.append({
            "source": "ceiling_rule",
            "description": f"Limit 8 kości: +{ceiling_bonus} do rzutu",
            "value": ceiling_bonus
        })

    return {
        "outcome": outcome,
        "R": total_result,
        "T": target_threshold,
        "available_skills": available_skills,
        "chosen_skill": subskill_code,
        "allow_skill_choice": action_meta.allow_skill_choice and len(available_skills) > 1,
        "details": {
            "pool_size": pool_size,
            "roll_result": roll_result,
            "flat_bonus": flat_bonus,
            "perk_bonus": perk_effects["roll_bonus"],
            "perk_skill_bonus": perk_effects["skill_bonus"],
            "situational_mod": situational_mod,
            "attr_level": attr_level,
            "skill_level": skill_level,
            "skill_code": subskill_code,
            "base_threshold": base_threshold,
            "special_effects": perk_effects["special_effects"],
            "detailed_sources": {
                "flat_bonus_sources": flat_bonus_sources,
                "perk_roll_bonus_sources": perk_effects["detailed_sources"]["roll_bonus_sources"],
                "perk_skill_bonus_sources": perk_effects["detailed_sources"]["skill_bonus_sources"],
                "total_calculation": f"{roll_result} (rzut) + {flat_bonus} (bonus) + {perk_effects['roll_bonus']} (perki) + {situational_mod} (sytuacyjny) = {total_result}"
            }
        }
    }
