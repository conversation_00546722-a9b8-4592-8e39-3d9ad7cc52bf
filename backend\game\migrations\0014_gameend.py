# Generated by Django 5.2.1 on 2025-06-23 10:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0013_alter_transition_unique_together_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameEnd',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=120)),
                ('end_type', models.CharField(choices=[('game', 'Koniec Gry'), ('chapter', 'Koniec Rozdziału'), ('subchapter', 'Koniec Podrozdziału')], default='game', help_text='Typ zakończenia', max_length=20)),
                ('message', models.TextField(blank=True, help_text='Wiadomość wyświetlana graczowi przy zakończeniu')),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text='<PERSON><PERSON> zakoń<PERSON>enie jest aktywne')),
            ],
            options={
                'verbose_name': 'game end',
                'verbose_name_plural': 'game ends',
                'ordering': ['end_type', 'title'],
            },
        ),
    ]
