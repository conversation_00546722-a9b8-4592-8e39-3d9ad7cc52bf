"""
game.logic.expr
~~~~~~~~~~~~~~~
A tiny DSL for *condition_expr* on transitions.

Syntax ­– intentionally kept close to a **very small** subset of Python:

    - Identifiers → come from the evaluation context (dict-like).
    - Literals     → int / float / str / bool.
    - Comparisons  → ==, !=, <, <=, >, >=, `in`, `not in`.
    - Boolean ops  → and, or, not.
    - Arithmetic   → +, -, *, /, %, //  (handy for simple maths).
    - Parentheses  → for grouping.

Everything else (function calls, attribute access, etc.) is rejected.
"""

from __future__ import annotations

import ast
import operator as _op
from typing import Any, Mapping, Sequence

__all__ = ["tokenize", "eval_expr", "ExprError"]


# ────────────────────────────────────────────────────────────────────────
# Public helpers
# ────────────────────────────────────────────────────────────────────────
class ExprError(ValueError):
    """Raised when the DSL string is invalid or contains disallowed syntax."""


def tokenize(expr: str) -> list[str]:
    """Return a **very** crude token list (mostly for debugging / tests)."""
    import re

    _TOK = re.compile(
        r"""
        \s*(?:                           # ignore leading whitespace
        ([A-Za-z_][\w]*) |               # 1. identifiers
        ([0-9]+(?:\.[0-9]+)?) |          # 2. numbers
        (==|!=|<=|>=|<|>|\/\/|[%*/()+-]) | # 3. operators / parens
        (and|or|not|in|not\s+in) |       # 4. keywords
        ("(?:\\.|[^"])*"|'(?:\\.|[^'])*') # 5. quoted string
        )""",
        re.X,
    )

    out: list[str] = []
    i = 0
    while i < len(expr):
        m = _TOK.match(expr, i)
        if not m:
            raise ExprError(f"unexpected token near: {expr[i:i+10]!r}")
        token = next(t for t in m.groups() if t is not None)
        out.append(token)
        i = m.end()
    return out


def eval_expr(expr: str, ctx: Mapping[str, Any] | None = None) -> bool:
    """
    Safely evaluate *expr* against a context dict.

    Any identifier not present in *ctx* resolves to **None**.
    """
    ctx = ctx or {}
    try:
        tree = ast.parse(expr, mode="eval")
    except SyntaxError as e:
        raise ExprError(str(e)) from None

    _CheckVisitor().visit(tree)  # security / sanity
    return _EvalVisitor(ctx).visit(tree.body)  # type: ignore[arg-type]


# ────────────────────────────────────────────────────────────────────────
# Internal helpers
# ────────────────────────────────────────────────────────────────────────
_ALLOWED_BINOPS = {
    ast.Add: _op.add,
    ast.Sub: _op.sub,
    ast.Mult: _op.mul,
    ast.Div: _op.truediv,
    ast.FloorDiv: _op.floordiv,
    ast.Mod: _op.mod,
}

_ALLOWED_CMPOPS = {
    ast.Eq: _op.eq,
    ast.NotEq: _op.ne,
    ast.Lt: _op.lt,
    ast.LtE: _op.le,
    ast.Gt: _op.gt,
    ast.GtE: _op.ge,
    ast.In: lambda a, b: a in b,
    ast.NotIn: lambda a, b: a not in b,
}

class _CheckVisitor(ast.NodeVisitor):
    """Reject everything that’s not on the tiny whitelist."""

    _ALLOWED = (
        ast.Expression,
        ast.BoolOp,
        ast.BinOp,
        ast.UnaryOp,
        ast.Compare,
        ast.Name,
        ast.Constant,
    )

    def generic_visit(self, node: ast.AST) -> None:  # noqa: D401
        if not isinstance(node, self._ALLOWED):
            raise ExprError(f"disallowed syntax: {ast.dump(node, include_attributes=False)}")
        super().generic_visit(node)


class _EvalVisitor(ast.NodeVisitor):
    """Evaluate the already-checked AST; all nodes are guaranteed safe."""

    def __init__(self, ctx: Mapping[str, Any]):
        super().__init__()
        self.ctx = ctx

    # Literals
    def visit_Constant(self, node: ast.Constant):  # type: ignore[override]
        return node.value

    # Identifiers
    def visit_Name(self, node: ast.Name):  # type: ignore[override]
        return self.ctx.get(node.id)

    # Unary  (!, -)
    def visit_UnaryOp(self, node: ast.UnaryOp):  # type: ignore[override]
        operand = self.visit(node.operand)
        if isinstance(node.op, ast.Not):
            return not operand
        if isinstance(node.op, ast.USub):
            return -operand
        if isinstance(node.op, ast.UAdd):
            return +operand
        raise ExprError("unsupported unary op")

    # Binary (+, -, *, /, //, %)
    def visit_BinOp(self, node: ast.BinOp):  # type: ignore[override]
        op_type = type(node.op)
        if op_type not in _ALLOWED_BINOPS:
            raise ExprError("unsupported operator")
        left, right = self.visit(node.left), self.visit(node.right)
        return _ALLOWED_BINOPS[op_type](left, right)

    # Boolean AND/OR
    def visit_BoolOp(self, node: ast.BoolOp):  # type: ignore[override]
        if isinstance(node.op, ast.And):
            for v in node.values:
                if not self.visit(v):
                    return False
            return True
        elif isinstance(node.op, ast.Or):
            for v in node.values:
                if self.visit(v):
                    return True
            return False
        raise ExprError("unsupported boolean op")

    # Comparisons
    def visit_Compare(self, node: ast.Compare):  # type: ignore[override]
        left = self.visit(node.left)
        for op, comparator in zip(node.ops, node.comparators):
            op_func = _ALLOWED_CMPOPS.get(type(op))
            if op_func is None:
                raise ExprError("unsupported comparison op")
            right = self.visit(comparator)
            if not op_func(left, right):
                return False
            left = right  # chain comparisons
        return True
