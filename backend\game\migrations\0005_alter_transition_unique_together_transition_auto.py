# Generated by Django 5.2.1 on 2025-05-21 16:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0004_savegame_slot_alter_savegame_unique_together'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='transition',
            unique_together={('origin', 'target', 'condition_expr')},
        ),
        migrations.AddField(
            model_name='transition',
            name='auto',
            field=models.BooleanField(default=False, help_text='If True, the resolver will take this transition immediately once condition_expr evaluates to True (no player choice needed).'),
        ),
    ]
