# ─────────────────────────── router / urls  ─────────────────────────
from rest_framework import routers
from django.urls import path, include 
from .viewsets import ChapterViewSet, SubchapterViewSet, SceneViewSet, TransitionViewSet, GameObjectViewSet, AttributeViewSet, SubskillViewSet, PerkViewSet, ActionMetaViewSet, GameEndViewSet
from .views import ProfileSaveView, SaveSlotListView, NewGameView
from .gameplay import PlayResolveView, PlayTransitionView, LevelUpView, AttributeProgressView, AttributeSkillChoiceView, AvailablePerksView

router = routers.DefaultRouter()
router.register(r"chapters", ChapterViewSet)
router.register(r"subchapters", SubchapterViewSet)
router.register(r"scenes", SceneViewSet, basename="scene")
router.register(r"transitions", TransitionViewSet)
router.register(r"objects", GameObjectViewSet)
router.register(r"attributes", AttributeViewSet)
router.register(r"subskills", SubskillViewSet)
router.register(r"perks", PerkViewSet)
router.register(r"actions", ActionMetaViewSet)
router.register(r"game-ends", GameEndViewSet)

urlpatterns = [
    path("", include(router.urls)),
    path("profile/save/",  ProfileSaveView.as_view(),  name="profile-save"),
    path("profile/saves/", SaveSlotListView.as_view(), name="profile-saves"),
    path("profile/newgame/", NewGameView.as_view(), name="new-game"),
    path("play/resolve/", PlayResolveView.as_view(), name="play-resolve"),
    path("play/transition/", PlayTransitionView.as_view(), name="play-transition"),
    path("play/levelup/", LevelUpView.as_view(), name="play-levelup"),
    path("play/progress/", AttributeProgressView.as_view(), name="attribute-progress"),
    path("play/attribute-skill/", AttributeSkillChoiceView.as_view(), name="attribute-skill-choice"),
    path("play/available-perks/", AvailablePerksView.as_view(), name="available-perks"),
]