from allauth.account.adapter import Default<PERSON><PERSON>untAdapter
from django.conf import settings


class EmailVerificationAccountAdapter(DefaultAccountAdapter):
    def save_user(self, request, user, form, commit=True):
        """
        Override to ensure users are inactive until email verification
        """
        user = super().save_user(request, user, form, commit=False)
        
        # Set user as inactive if email verification is mandatory
        if getattr(settings, 'ACCOUNT_EMAIL_VERIFICATION', None) == 'mandatory':
            user.is_active = False
        
        if commit:
            user.save()
        return user
