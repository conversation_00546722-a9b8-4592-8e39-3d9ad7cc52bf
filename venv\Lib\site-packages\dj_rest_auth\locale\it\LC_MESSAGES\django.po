# Italian language.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-07 09:46+0200\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: .\registration\serializers.py:67
msgid "View is not defined, pass it as a context variable"
msgstr "\"view\" non è definito, passalo come variabile di contesto"

#: .\registration\serializers.py:72
msgid "Define adapter_class in view"
msgstr "Definisci \"adapter_class\" in view"

#: .\registration\serializers.py:91
msgid "Define callback_url in view"
msgstr "Definisci \"callback_url\" in view"

#: .\registration\serializers.py:95
msgid "Define client_class in view"
msgstr "Definisci \"client_class\" in view"

#: .\registration\serializers.py:116
msgid "Incorrect input. access_token or code is required."
msgstr "Input errato. È richiesto \"access_token\" o \"code\"."

#: .\registration\serializers.py:125
msgid "Incorrect value"
msgstr "Valore errato"

#: .\registration\serializers.py:139
msgid "User is already registered with this e-mail address."
msgstr "Un altro utente è già registrato con questo indirizzo e-mail."

#: .\registration\serializers.py:185
msgid "A user is already registered with this e-mail address."
msgstr "Un altro utente è già registrato con questo indirizzo e-mail."

#: .\registration\serializers.py:193
msgid "The two password fields didn't match."
msgstr "Le password non corrispondono."

#: .\registration\views.py:47
msgid "Verification e-mail sent."
msgstr "E-mail di verifica inviata."

#: .\registration\views.py:95
msgid "ok"
msgstr "ok"

#: .\serializers.py:32
msgid "Must include \"email\" and \"password\"."
msgstr "Deve includere \"email\" e \"password\"."

#: .\serializers.py:43
msgid "Must include \"username\" and \"password\"."
msgstr "Deve includere \"email\" e \"password\"."

#: .\serializers.py:56
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr "Deve includere o \"username\" o \"email\" e \"password\"."

#: .\serializers.py:97
msgid "User account is disabled."
msgstr "L'account è disabilitato."

#: .\serializers.py:100
msgid "Unable to log in with provided credentials."
msgstr "Impossibile accedere con le credenziali fornite."

#: .\serializers.py:109
msgid "E-mail is not verified."
msgstr "L'e-mail non è verificata."

#: .\serializers.py:264
msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "La tua password precedente non è corretta. Inseriscila nuovamente."

#: .\views.py:139
msgid "Successfully logged out."
msgstr "Disconnesso con successo."

#: .\views.py:161
msgid "Refresh token was not included in request data."
msgstr "Il \"Refresh token\" non è presente nei dati della richiesta."

#: .\views.py:171 .\views.py:175
msgid "An error has occurred."
msgstr "Si è verificato un errore."

#: .\views.py:180
msgid ""
"Neither cookies or blacklist are enabled, so the token has not been deleted "
"server side. Please make sure the token is deleted client side."
msgstr ""
"Né i cookies né la blacklist sono abilitati, quindi il token non è stato eliminato "
"lato server. Assicurarsi che il token sia eliminato lato client."

#: .\views.py:230
msgid "Password reset e-mail has been sent."
msgstr "L'e-mail per il recupero della password è stata inviata."

#: .\views.py:256
msgid "Password has been reset with the new password."
msgstr "Password aggiornata."

#: .\views.py:278
msgid "New password has been saved."
msgstr "Nuova password salvata."
