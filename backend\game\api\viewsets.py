# ─────────────────────────── viewsets ───────────────────────────────
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from django.core.exceptions import ValidationError as DjangoValidationError

from game.models import (
    Chapter, Subchapter, Scene, Transition, GameObject,
    Attribute, Subskill, Perk, ActionMeta, GameEnd,
)

from .serializers import (
    ChapterSerializer, ChapterDetailSerializer,
    SubchapterSerializer, SubchapterDetailSerializer,
    SceneSerializer, TransitionSerializer, GameObjectSerializer,
    AttributeSerializer, SubskillSerializer, PerkSerializer,
    ActionMetaSerializer, GameEndSerializer,
)
from .permissions import ReadOnlyOrStaff
from game.utils.logging import GameLogger, log_api_operation, log_api_error
from game.utils.exceptions import GameValidationError, GameNotFoundError


class SceneViewSet(viewsets.ModelViewSet):
    queryset = Scene.objects.all().prefetch_related("game_objects")
    serializer_class = SceneSerializer
    permission_classes = [ReadOnlyOrStaff]
    lookup_field = "slug"

    def create(self, request, *args, **kwargs):
        """Create a new scene with proper validation and logging."""
        try:
            log_api_operation(
                "CREATE", "Scene",
                user=request.user,
                title=request.data.get('title'),
                subchapter=request.data.get('subchapter')
            )

            # Validate required fields
            if not request.data.get('title'):
                raise GameValidationError("Title is required", field="title")

            response = super().create(request, *args, **kwargs)

            GameLogger.info(
                "Scene created successfully",
                user=request.user,
                scene_slug=response.data.get('slug'),
                scene_title=response.data.get('title')
            )
            return response

        except ValidationError as e:
            log_api_error("CREATE", "Scene", str(e), user=request.user)
            return Response({
                'error': True,
                'message': 'Invalid scene data',
                'details': e.detail if hasattr(e, 'detail') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except GameValidationError as e:
            log_api_error("CREATE", "Scene", e.message, user=request.user, field=e.field)
            return Response({
                'error': True,
                'message': e.message,
                'field': e.field
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            log_api_error("CREATE", "Scene", str(e), user=request.user)
            return Response({
                'error': True,
                'message': 'Failed to create scene'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def retrieve(self, request, *args, **kwargs):
        """Override retrieve to reset scene actions only on new visits (not refresh)."""
        scene = self.get_object()

        # Reset executed actions only if this is a new scene visit
        if request.user.is_authenticated:
            from game.logic.xp import reset_scene_actions_if_new_visit
            is_new_visit = reset_scene_actions_if_new_visit(request.user, scene.slug)
            if is_new_visit:
                print(f"🆕 New visit to scene: {scene.slug}")
            else:
                print(f"🔄 Refresh in scene: {scene.slug}")

        return super().retrieve(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        """Update scene with proper validation and logging."""
        scene_slug = kwargs.get('slug', 'unknown')
        try:
            log_api_operation(
                "UPDATE", "Scene",
                user=request.user,
                scene_slug=scene_slug,
                fields=list(request.data.keys())
            )

            response = super().update(request, *args, **kwargs)

            GameLogger.info(
                "Scene updated successfully",
                user=request.user,
                scene_slug=scene_slug
            )
            return response

        except ValidationError as e:
            log_api_error("UPDATE", "Scene", str(e), user=request.user, scene_slug=scene_slug)
            return Response({
                'error': True,
                'message': 'Invalid scene data',
                'details': e.detail if hasattr(e, 'detail') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            log_api_error("UPDATE", "Scene", str(e), user=request.user, scene_slug=scene_slug)
            return Response({
                'error': True,
                'message': 'Failed to update scene'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def partial_update(self, request, *args, **kwargs):
        """Partially update scene with proper validation and logging."""
        scene_slug = kwargs.get('slug', 'unknown')
        try:
            log_api_operation(
                "PARTIAL_UPDATE", "Scene",
                user=request.user,
                scene_slug=scene_slug,
                fields=list(request.data.keys())
            )

            response = super().partial_update(request, *args, **kwargs)

            GameLogger.info(
                "Scene partially updated successfully",
                user=request.user,
                scene_slug=scene_slug
            )
            return response

        except ValidationError as e:
            log_api_error("PARTIAL_UPDATE", "Scene", str(e), user=request.user, scene_slug=scene_slug)
            return Response({
                'error': True,
                'message': 'Invalid scene data',
                'details': e.detail if hasattr(e, 'detail') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            log_api_error("PARTIAL_UPDATE", "Scene", str(e), user=request.user, scene_slug=scene_slug)
            return Response({
                'error': True,
                'message': 'Failed to update scene'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TransitionViewSet(viewsets.ModelViewSet):
    queryset = Transition.objects.select_related("origin", "target", "target_chapter", "target_subchapter")
    serializer_class = TransitionSerializer
    permission_classes = [ReadOnlyOrStaff]

    def create(self, request, *args, **kwargs):
        """Create a new transition with proper validation and logging."""
        try:
            log_api_operation(
                "CREATE", "Transition",
                user=request.user,
                origin=request.data.get('origin'),
                transition_type=request.data.get('transition_type')
            )

            response = super().create(request, *args, **kwargs)

            GameLogger.info(
                "Transition created successfully",
                user=request.user,
                transition_id=response.data.get('id'),
                origin=request.data.get('origin')
            )
            return response

        except ValidationError as e:
            log_api_error("CREATE", "Transition", str(e), user=request.user)
            return Response({
                'error': True,
                'message': 'Invalid transition data',
                'details': e.detail if hasattr(e, 'detail') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            log_api_error("CREATE", "Transition", str(e), user=request.user)
            return Response({
                'error': True,
                'message': 'Failed to create transition'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['delete'], url_path='bulk-delete')
    def bulk_delete(self, request):
        """Bulk delete transitions by origin scene."""
        origin = request.query_params.get('origin')
        if not origin:
            GameLogger.warning("Bulk delete attempted without origin", user=request.user)
            return Response({
                'error': True,
                'message': 'Origin scene required for bulk delete'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            deleted_count = Transition.objects.filter(origin__slug=origin).count()
            Transition.objects.filter(origin__slug=origin).delete()

            GameLogger.info(
                f"Bulk deleted {deleted_count} transitions",
                user=request.user,
                origin=origin,
                deleted_count=deleted_count
            )
            return Response({'deleted': deleted_count}, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            log_api_error("BULK_DELETE", "Transition", str(e), user=request.user, origin=origin)
            return Response({
                'error': True,
                'message': 'Failed to delete transitions'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_queryset(self):
        """Filtrowanie po origin i target przez query params"""
        queryset = super().get_queryset()
        origin = self.request.query_params.get('origin')
        target = self.request.query_params.get('target')
        from_scene = self.request.query_params.get('from_scene')  # Alternative param name

        if origin:
            queryset = queryset.filter(origin__slug=origin)
        if from_scene:
            queryset = queryset.filter(origin__slug=from_scene)
        if target:
            queryset = queryset.filter(target__slug=target)

        return queryset

    def destroy(self, request, *args, **kwargs):
        """Override destroy to handle bulk delete by query params"""
        # Check if this is a bulk delete request (no pk provided)
        if 'pk' not in kwargs:
            origin = request.query_params.get('origin')
            if origin:
                # Bulk delete all transitions from this scene
                deleted_count = self.get_queryset().filter(origin__slug=origin).delete()[0]
                print(f"🗑️ Bulk deleted {deleted_count} transitions for scene: {origin}")
                return Response({'deleted': deleted_count}, status=204)
            else:
                return Response({'error': 'origin parameter required for bulk delete'}, status=400)

        # Regular single delete
        return super().destroy(request, *args, **kwargs)


class GameObjectViewSet(viewsets.ModelViewSet):
    queryset = GameObject.objects.select_related("scene")
    serializer_class = GameObjectSerializer
    permission_classes = [ReadOnlyOrStaff]
    filterset_fields = ["scene__slug", "type"]


class AttributeViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Attribute.objects.all()
    serializer_class = AttributeSerializer


class SubskillViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Subskill.objects.select_related("primary_attr")
    serializer_class = SubskillSerializer


class PerkViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Perk.objects.all()
    serializer_class = PerkSerializer
    filterset_fields = ["tags"]


class ActionMetaViewSet(viewsets.ModelViewSet):
    queryset = ActionMeta.objects.select_related("attribute", "subskill")
    serializer_class = ActionMetaSerializer
    permission_classes = [ReadOnlyOrStaff]

    def create(self, request, *args, **kwargs):
        """Create a new action with proper validation and logging."""
        try:
            log_api_operation(
                "CREATE", "ActionMeta",
                user=request.user,
                action_id=request.data.get('id'),
                action_type=request.data.get('action_type')
            )

            response = super().create(request, *args, **kwargs)

            GameLogger.info(
                "Action created successfully",
                user=request.user,
                action_id=response.data.get('id'),
                action_type=response.data.get('action_type')
            )
            return response

        except ValidationError as e:
            log_api_error("CREATE", "ActionMeta", str(e), user=request.user)
            return Response({
                'error': True,
                'message': 'Invalid action data',
                'details': e.detail if hasattr(e, 'detail') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            log_api_error("CREATE", "ActionMeta", str(e), user=request.user)
            return Response({
                'error': True,
                'message': 'Failed to create action'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_queryset(self):
        """Filtrowanie po scene przez query params"""
        queryset = super().get_queryset()
        scene = self.request.query_params.get('scene')

        if scene:
            # Filter by scene slug - but ActionMeta doesn't have direct scene relation
            # This is for loading existing actions, not creating new ones
            pass

        return queryset


class GameEndViewSet(viewsets.ModelViewSet):
    queryset = GameEnd.objects.filter(is_active=True)
    serializer_class = GameEndSerializer
    permission_classes = [ReadOnlyOrStaff]


class ChapterViewSet(viewsets.ModelViewSet):
    queryset = Chapter.objects.filter(is_active=True).prefetch_related('subchapters')
    permission_classes = [ReadOnlyOrStaff]

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return ChapterDetailSerializer
        return ChapterSerializer


class SubchapterViewSet(viewsets.ModelViewSet):
    queryset = Subchapter.objects.filter(is_active=True).select_related('chapter').prefetch_related('scenes')
    permission_classes = [ReadOnlyOrStaff]
    filterset_fields = ['chapter']

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return SubchapterDetailSerializer
        return SubchapterSerializer

    def update(self, request, *args, **kwargs):
        """Update subchapter with proper validation and logging."""
        subchapter_id = kwargs.get('pk', 'unknown')
        try:
            log_api_operation(
                "UPDATE", "Subchapter",
                user=request.user,
                subchapter_id=subchapter_id,
                fields=list(request.data.keys())
            )

            response = super().update(request, *args, **kwargs)

            GameLogger.info(
                "Subchapter updated successfully",
                user=request.user,
                subchapter_id=subchapter_id
            )
            return response

        except ValidationError as e:
            log_api_error("UPDATE", "Subchapter", str(e), user=request.user, subchapter_id=subchapter_id)
            return Response({
                'error': True,
                'message': 'Invalid subchapter data',
                'details': e.detail if hasattr(e, 'detail') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            log_api_error("UPDATE", "Subchapter", str(e), user=request.user, subchapter_id=subchapter_id)
            return Response({
                'error': True,
                'message': 'Failed to update subchapter'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def partial_update(self, request, *args, **kwargs):
        """Partially update subchapter with proper validation and logging."""
        subchapter_id = kwargs.get('pk', 'unknown')
        try:
            log_api_operation(
                "PARTIAL_UPDATE", "Subchapter",
                user=request.user,
                subchapter_id=subchapter_id,
                fields=list(request.data.keys())
            )

            response = super().partial_update(request, *args, **kwargs)

            GameLogger.info(
                "Subchapter partially updated successfully",
                user=request.user,
                subchapter_id=subchapter_id
            )
            return response

        except ValidationError as e:
            log_api_error("PARTIAL_UPDATE", "Subchapter", str(e), user=request.user, subchapter_id=subchapter_id)
            return Response({
                'error': True,
                'message': 'Invalid subchapter data',
                'details': e.detail if hasattr(e, 'detail') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            log_api_error("PARTIAL_UPDATE", "Subchapter", str(e), user=request.user, subchapter_id=subchapter_id)
            return Response({
                'error': True,
                'message': 'Failed to update subchapter'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
