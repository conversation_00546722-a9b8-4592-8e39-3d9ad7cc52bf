from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from allauth.account.models import EmailAddress

User = get_user_model()


class Command(BaseCommand):
    help = 'Manually verify a user email address'

    def add_arguments(self, parser):
        parser.add_argument('email', type=str, help='Email address to verify')

    def handle(self, *args, **options):
        email = options['email']
        
        try:
            user = User.objects.get(email=email)
            email_address, created = EmailAddress.objects.get_or_create(
                user=user,
                email=email,
                defaults={'verified': True, 'primary': True}
            )
            
            if not created and not email_address.verified:
                email_address.verified = True
                email_address.save()
                # Also activate the user
                user.is_active = True
                user.save()
                self.stdout.write(
                    self.style.SUCCESS(f'Email {email} has been verified and user activated')
                )
            elif email_address.verified:
                self.stdout.write(
                    self.style.WARNING(f'Email {email} was already verified')
                )
            else:
                # Also activate the user
                user.is_active = True
                user.save()
                self.stdout.write(
                    self.style.SUCCESS(f'Email {email} has been verified and user activated')
                )
                
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User with email {email} does not exist')
            )
