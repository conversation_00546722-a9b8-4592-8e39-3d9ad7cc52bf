"""
Centralized logging utilities for the game application.
"""
import logging
from typing import Any, Dict, Optional
from django.contrib.auth import get_user_model

User = get_user_model()

# Get logger for game module
logger = logging.getLogger('game')


class GameLogger:
    """Centralized logger for game operations with structured logging."""
    
    @staticmethod
    def _format_extra(user: Optional[User] = None, **kwargs) -> Dict[str, Any]:
        """Format extra data for structured logging."""
        extra = {}
        
        if user and hasattr(user, 'id'):
            extra['user_id'] = user.id
            extra['username'] = getattr(user, 'username', 'unknown')
        
        # Add any additional context
        extra.update(kwargs)
        
        return {'extra': extra} if extra else {}
    
    @staticmethod
    def info(message: str, user: Optional[User] = None, **kwargs):
        """Log info level message with optional user context."""
        extra = GameLogger._format_extra(user, **kwargs)
        logger.info(message, **extra)
    
    @staticmethod
    def warning(message: str, user: Optional[User] = None, **kwargs):
        """Log warning level message with optional user context."""
        extra = GameLogger._format_extra(user, **kwargs)
        logger.warning(message, **extra)
    
    @staticmethod
    def error(message: str, user: Optional[User] = None, **kwargs):
        """Log error level message with optional user context."""
        extra = GameLogger._format_extra(user, **kwargs)
        logger.error(message, **extra)
    
    @staticmethod
    def debug(message: str, user: Optional[User] = None, **kwargs):
        """Log debug level message with optional user context."""
        extra = GameLogger._format_extra(user, **kwargs)
        logger.debug(message, **extra)


# Convenience functions for common operations
def log_api_operation(operation: str, model: str, user: Optional[User] = None, **kwargs):
    """Log API operation with standardized format."""
    GameLogger.info(
        f"API {operation}: {model}",
        user=user,
        operation=operation,
        model=model,
        **kwargs
    )


def log_api_error(operation: str, model: str, error: str, user: Optional[User] = None, **kwargs):
    """Log API error with standardized format."""
    GameLogger.error(
        f"API {operation} failed: {model} - {error}",
        user=user,
        operation=operation,
        model=model,
        error=error,
        **kwargs
    )


def log_validation_error(field: str, value: Any, error: str, user: Optional[User] = None):
    """Log validation error with field context."""
    GameLogger.warning(
        f"Validation error: {field} = {value} - {error}",
        user=user,
        field=field,
        value=str(value),
        validation_error=error
    )
