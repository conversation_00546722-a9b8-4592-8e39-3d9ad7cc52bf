# game/urls.py
from django.urls import include, path
from game.api.gameplay import RollActionView, PlayResolveView, LevelUpView, ActionSkillsView

urlpatterns = [
    path("", include("game.api.router")),

    path("play/roll/", RollActionView.as_view(), name="roll-action"),
    path("play/levelup/", LevelUpView.as_view(), name="play-levelup"),
    path("play/action-skills/", ActionSkillsView.as_view(), name="action-skills"),
    path("api/v1/play/resolve/", PlayResolveView.as_view()),
]
