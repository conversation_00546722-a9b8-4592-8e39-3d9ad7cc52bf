# Generated by Django 5.2.1 on 2025-06-20 21:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0011_perk_effect_tags_perk_effect_type_perk_effect_value_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Chapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=120)),
                ('description', models.TextField(blank=True)),
                ('order', models.PositiveIntegerField(default=1, help_text='Kolejność rozdziału w grze')),
                ('is_active', models.BooleanField(default=True, help_text='Czy rozdział jest aktywny')),
            ],
            options={
                'verbose_name': 'chapter',
                'verbose_name_plural': 'chapters',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Subchapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=120)),
                ('description', models.TextField(blank=True)),
                ('order', models.PositiveIntegerField(default=1, help_text='Kolejność podrozdziału w rozdziale')),
                ('is_active', models.BooleanField(default=True, help_text='Czy podrozdział jest aktywny')),
                ('chapter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subchapters', to='game.chapter')),
            ],
            options={
                'verbose_name': 'subchapter',
                'verbose_name_plural': 'subchapters',
                'ordering': ['chapter__order', 'order'],
                'unique_together': {('chapter', 'order')},
            },
        ),
        migrations.AddField(
            model_name='scene',
            name='subchapter',
            field=models.ForeignKey(blank=True, help_text='Podrozdział do którego należy scena', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scenes', to='game.subchapter'),
        ),
    ]
