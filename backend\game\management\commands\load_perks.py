"""
Management command to load perks from the perks file into the database.

Usage:
    python manage.py load_perks

This command will:
1. Read perk definitions from the perks file
2. Create or update perks in the database
3. Handle level ranges and tags properly
4. Be idempotent (safe to run multiple times)
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from game.models import Perk


class Command(BaseCommand):
    help = "Load perks from perks file into database"

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating it',
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing perks instead of skipping them',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        update_existing = options['update']
        
        # Perk definitions from the perks file
        perks_data = [
            # Level 1–2
            {"id": "quick_reflexes", "name": "<PERSON><PERSON>b<PERSON> refleks", "description": "+10% szansy na unik w akcjach wymagających zręczności.", "levelRange": [1, 2], "tags": ["dex", "evasion"]},
            {"id": "wrestler", "name": "Zapaśnik", "description": "+20% szansy sukcesu w walce wręcz.", "levelRange": [1, 2], "tags": ["str", "melee"]},
            {"id": "clever_bluff", "name": "Błyskotliwy blef", "description": "+15% do skuteczności blefów.", "levelRange": [1, 2], "tags": ["cha", "dialogue"]},
            {"id": "sniper_instinct", "name": "Zmysł snajpera", "description": "+10% szansy na trafienie w strzale dystansowym.", "levelRange": [1, 2], "tags": ["dex", "firearms"]},
            {"id": "amateur_assassin", "name": "Skrytobójca amator", "description": "Ciche ataki mają +15% szansy sukcesu.", "levelRange": [1, 2], "tags": ["dex", "stealth"]},
            {"id": "technical_touch", "name": "Techniczny zmysł", "description": "2x większa szansa na rozwój umiejętności technicznych.", "levelRange": [1, 2], "tags": ["tec"]},
            {"id": "calm_mind", "name": "Chłodna głowa", "description": "Pierwszy test WIL w scenie zawsze udany.", "levelRange": [1, 2], "tags": ["wil"]},
            {"id": "tactical_foresight", "name": "Taktyczny krok", "description": "Możesz podglądać wyniki testów DEX przed wyborem akcji.", "levelRange": [1, 2], "tags": ["dex", "analysis"]},
            {"id": "data_collector", "name": "Zbieracz danych", "description": "Każde wydarzenie daje dodatkowy punkt PER lub TEC (losowo).", "levelRange": [1, 2], "tags": ["per", "tec"]},
            {"id": "combat_readiness", "name": "Sprawność bojowa", "description": "+5% do wszystkich testów fizycznych (STR, DEX).", "levelRange": [1, 2], "tags": ["str", "dex"]},

            # Level 3–4
            {"id": "lie_master", "name": "Zawodowy kłamca", "description": "Raz na scenę możesz wymusić sukces w dialogu.", "levelRange": [3, 4], "tags": ["cha", "dialogue"]},
            {"id": "camera_buster", "name": "Pogromca kamer", "description": "Wrogie systemy rozpoznawania nie widzą cię przez 1 turę.", "levelRange": [3, 4], "tags": ["tec", "stealth"]},
            {"id": "parkour_master", "name": "Mistrz parkouru", "description": "Ucieczki i uniki +20% skuteczności.", "levelRange": [3, 4], "tags": ["dex", "evasion"]},
            {"id": "black_bag", "name": "Czarna torba", "description": "Możesz zneutralizować cel bez zabijania (dyplomatyczne misje).", "levelRange": [3, 4], "tags": ["str", "cha"]},
            {"id": "terrain_sense", "name": "Zmysł terenowy", "description": "+25% szansy na wykrycie zagrożeń.", "levelRange": [3, 4], "tags": ["per", "analysis"]},
            {"id": "interrogator", "name": "Skuteczny przesłuchiwacz", "description": "+15% szansy na sukces przy przesłuchaniu.", "levelRange": [3, 4], "tags": ["cha", "dialogue"]},
            {"id": "improvisation_expert", "name": "Ekspert improwizacji", "description": "+10% do testów bez wymaganej umiejętności.", "levelRange": [3, 4], "tags": ["wil", "tec"]},
            {"id": "lucky_shot", "name": "Szczęśliwy strzał", "description": "Strzały mają 5% szansy na eliminację celu.", "levelRange": [3, 4], "tags": ["dex", "firearms"]},
            {"id": "data_noise", "name": "Szmer danych", "description": "Rozmowy z AI dają więcej tropów fabularnych.", "levelRange": [3, 4], "tags": ["tec", "ai"]},
            {"id": "intimidating_presence", "name": "Mocna postura", "description": "+15% szansy na zastraszenie.", "levelRange": [3, 4], "tags": ["str", "cha"]},

            # Level 5–6
            {"id": "silent_steps", "name": "Bezszelestny krok", "description": "+30% skuteczności skradania.", "levelRange": [5, 6], "tags": ["dex", "stealth"]},
            {"id": "logical_override", "name": "Wymuszenie logiczne", "description": "Raz na misję zmuszasz AI do sprzeczności.", "levelRange": [5, 6], "tags": ["wil", "ai"]},
            {"id": "close_quarters_expert", "name": "Ekspert od zbliżeń", "description": "+10% skuteczności testów w bliskim dystansie.", "levelRange": [5, 6], "tags": ["melee", "firearms"]},
            {"id": "tech_commando", "name": "Komandos techniczny", "description": "Możesz użyć sabotażu lub śledzenia w każdej scenie.", "levelRange": [5, 6], "tags": ["tec", "stealth"]},
            {"id": "cipher_master", "name": "Mistrz szyfrów", "description": "+25% skuteczności w hakowaniu trudnych systemów.", "levelRange": [5, 6], "tags": ["tec", "hacking"]},
            {"id": "instinct_shot", "name": "Strzał intuicyjny", "description": "Możesz wykonać atak przed wyborem opcji.", "levelRange": [5, 6], "tags": ["dex", "firearms"]},
            {"id": "iron_loyalty", "name": "Lojalność do bólu", "description": "Wrogowie rzadziej cię zdradzają.", "levelRange": [5, 6], "tags": ["cha", "dialogue"]},
            {"id": "backup_plan", "name": "Zapasowy plan", "description": "Raz na 2 misje możesz cofnąć ostatni wybór.", "levelRange": [5, 6], "tags": ["wil", "analysis"]},
            {"id": "observer", "name": "Obserwator", "description": "Zyskujesz nowe opcje dostępne po PER.", "levelRange": [5, 6], "tags": ["per", "analysis"]},
            {"id": "sleeve_trick", "name": "Sztuczka z rękawa", "description": "Ukryta opcja dialogowa raz na scenę.", "levelRange": [5, 6], "tags": ["cha", "dialogue"]},

            # Level 7–8
            {"id": "logic_killer", "name": "Zabójca logiczny", "description": "AI popełnia błędy po każdej scenie z tobą.", "levelRange": [7, 8], "tags": ["ai", "psyche"]},
            {"id": "photographic_memory", "name": "Pamięć fotograficzna", "description": "Każda scena ujawnia więcej danych i skrótów.", "levelRange": [7, 8], "tags": ["per", "analysis"]},
            {"id": "extreme_training", "name": "Trening ekstremalny", "description": "+10% do wszystkich rozwinięć umiejętności.", "levelRange": [7, 8], "tags": ["str", "dex", "tec"]},
            {"id": "mimic_master", "name": "Mistrz mimikry", "description": "Podszywanie się pod dowolną rolę (1 raz/misja).", "levelRange": [7, 8], "tags": ["cha", "stealth"]},
            {"id": "preventive_action", "name": "Działanie prewencyjne", "description": "Przewidujesz pierwsze zagrożenie w scenie.", "levelRange": [7, 8], "tags": ["per", "wil"]},
            {"id": "radio_silence", "name": "Cisza radiowa", "description": "Drony i AI potrzebują 2 tur, by cię wykryć.", "levelRange": [7, 8], "tags": ["stealth", "ai"]},
            {"id": "precision_strike", "name": "Uderzenie precyzyjne", "description": "Odblokowuje atak w słaby punkt.", "levelRange": [7, 8], "tags": ["melee", "firearms"]},
            {"id": "data_assimilation", "name": "Asymilacja danych", "description": "Interakcje z AI rozwijają WIL szybciej.", "levelRange": [7, 8], "tags": ["wil", "ai"]},
            {"id": "magnetic_personality", "name": "Magnetyczna osobowość", "description": "Dialogi zaczynają się z bonusem CHA.", "levelRange": [7, 8], "tags": ["cha", "dialogue"]},
            {"id": "auto_response", "name": "Automatyczna odpowiedź", "description": "Pierwszy test w każdej scenie jest udany.", "levelRange": [7, 8], "tags": ["psyche", "evasion"]},

            # Level 9–10
            {"id": "logic_breaker", "name": "Złamana logika", "description": "Raz możesz wygrać scenę pułapką logiczną na AI.", "levelRange": [9, 10], "tags": ["ai", "psyche"]},
            {"id": "world_hack", "name": "Zhakowany świat", "description": "Przejmujesz infrastrukturę miasta na 1 scenę.", "levelRange": [9, 10], "tags": ["tec", "ai"]},
            {"id": "silent_before_storm", "name": "Cisza przed burzą", "description": "Odblokowuje zakończenie solo vs AI.", "levelRange": [9, 10], "tags": ["final", "psyche"]},
            {"id": "mental_domination", "name": "Mentalna dominacja", "description": "Możesz przejąć AI podrzędną jako sojusznika.", "levelRange": [9, 10], "tags": ["wil", "ai"]},
            {"id": "super_consolidation", "name": "Superkonsolidacja", "description": "+1 do wszystkich statystyk co poziom.", "levelRange": [9, 10], "tags": ["str", "dex", "tec", "cha", "per", "wil"]},
            {"id": "great_intuition", "name": "Wielka intuicja", "description": "Wyświetla prawdopodobieństwo sukcesu akcji.", "levelRange": [9, 10], "tags": ["analysis", "per"]},
            {"id": "unbreakable", "name": "Nieugięty", "description": "Testy WIL są o 25% łatwiejsze.", "levelRange": [9, 10], "tags": ["wil", "psyche"]},
            {"id": "destruction_machine", "name": "Maszyna destrukcji", "description": "Raz na misję możesz zniszczyć dowolny system.", "levelRange": [9, 10], "tags": ["str", "tec"]},
            {"id": "chaotic_pattern", "name": "Sztuczny chaos", "description": "AI nie przewiduje twoich działań.", "levelRange": [9, 10], "tags": ["ai", "stealth"]},
            {"id": "ai_sense", "name": "Połączenie duchowe", "description": "Czujesz obecność AI w każdej scenie.", "levelRange": [9, 10], "tags": ["per", "ai"]},
        ]
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be made"))
        
        created_count = 0
        updated_count = 0
        skipped_count = 0
        
        with transaction.atomic():
            for perk_data in perks_data:
                perk_id = perk_data["id"]
                level_range = perk_data["levelRange"]
                
                # Convert levelRange to level_min and level_max
                level_min = level_range[0]
                level_max = level_range[1]
                
                perk_defaults = {
                    "name": perk_data["name"],
                    "description": perk_data["description"],
                    "level_min": level_min,
                    "level_max": level_max,
                    "tags": perk_data["tags"],
                }
                
                if dry_run:
                    exists = Perk.objects.filter(id=perk_id).exists()
                    if exists:
                        if update_existing:
                            self.stdout.write(f"Would UPDATE: {perk_id} - {perk_data['name']}")
                            updated_count += 1
                        else:
                            self.stdout.write(f"Would SKIP: {perk_id} - {perk_data['name']} (already exists)")
                            skipped_count += 1
                    else:
                        self.stdout.write(f"Would CREATE: {perk_id} - {perk_data['name']}")
                        created_count += 1
                else:
                    perk, created = Perk.objects.get_or_create(
                        id=perk_id,
                        defaults=perk_defaults
                    )
                    
                    if created:
                        self.stdout.write(
                            self.style.SUCCESS(f"Created perk: {perk_id} - {perk.name}")
                        )
                        created_count += 1
                    else:
                        if update_existing:
                            # Update existing perk
                            for field, value in perk_defaults.items():
                                setattr(perk, field, value)
                            perk.save()
                            self.stdout.write(
                                self.style.WARNING(f"Updated perk: {perk_id} - {perk.name}")
                            )
                            updated_count += 1
                        else:
                            self.stdout.write(f"Skipped existing perk: {perk_id} - {perk.name}")
                            skipped_count += 1
        
        # Summary
        self.stdout.write("\n" + "="*50)
        self.stdout.write(f"SUMMARY:")
        self.stdout.write(f"Created: {created_count}")
        self.stdout.write(f"Updated: {updated_count}")
        self.stdout.write(f"Skipped: {skipped_count}")
        self.stdout.write(f"Total processed: {len(perks_data)}")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("\nThis was a DRY RUN - no actual changes were made"))
            self.stdout.write("Run without --dry-run to apply changes")
        else:
            self.stdout.write(self.style.SUCCESS(f"\nSuccessfully processed {len(perks_data)} perks!"))
