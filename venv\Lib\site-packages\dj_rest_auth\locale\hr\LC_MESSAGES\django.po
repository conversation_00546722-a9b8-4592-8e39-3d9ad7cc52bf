# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-03-05 21:56-0800\n"
"PO-Revision-Date: 2016-08-01 07:48+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"
"X-Generator: Poedit 1.8.5\n"

#: registration/serializers.py:53
msgid "View is not defined, pass it as a context variable"
msgstr "Pogled nije definiran, proslijedite ga kao kontekstnu varijablu"

#: registration/serializers.py:58
msgid "Define adapter_class in view"
msgstr "Definirajte adapter_class u view"

#: registration/serializers.py:77
msgid "Define callback_url in view"
msgstr "Ustanovite callback_url u view"

#: registration/serializers.py:81
msgid "Define client_class in view"
msgstr "Ustanovite client_class u view"

#: registration/serializers.py:102
msgid "Incorrect input. access_token or code is required."
msgstr "Pogrešan unos. potreban je access_token ili kôd."

#: registration/serializers.py:111
msgid "Incorrect value"
msgstr "Netočna vrijednost"

#: registration/serializers.py:140
msgid "A user is already registered with this e-mail address."
msgstr "Korisnik je već registriran s ovom adresom e-mail"

#: registration/serializers.py:148
msgid "The two password fields didn't match."
msgstr "Dva polja za lozinku nisu se podudarala."

#: registration/views.py:44
msgid "Verification e-mail sent."
msgstr "Poslat je e-mail s potvrdom."

#: registration/views.py:91
msgid "ok"
msgstr "ок"

#: serializers.py:30
msgid "Must include \"email\" and \"password\"."
msgstr "Mora sadržavati \"e-mail \" i \"lozinku \"."

#: serializers.py:41
msgid "Must include \"username\" and \"password\"."
msgstr "Mora sadržavati \"username\" i \"password\"."

#: serializers.py:54
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr "Mora sadržavati bilo koje \"username\" ili \"email\" i \"password\"."

#: serializers.py:95
msgid "User account is disabled."
msgstr "Korisnički račun je onemogućen."

#: serializers.py:98
msgid "Unable to log in with provided credentials."
msgstr "Nije moguće prijaviti se s navedenim vjerodajnicama."

#: serializers.py:107
msgid "E-mail is not verified."
msgstr "E-mail nije provjerena"

#: views.py:126
msgid "Successfully logged out."
msgstr "Uspješno odjavljeni."

#: views.py:174
msgid "Password reset e-mail has been sent."
msgstr "Poslana je e-mail za ponovno postavljanje lozinke."

#: views.py:200
msgid "Password has been reset with the new password."
msgstr "Lozinka je resetirana novom lozinkom."

#: views.py:222
msgid "New password has been saved."
msgstr "Nova lozinka je spremljena."
