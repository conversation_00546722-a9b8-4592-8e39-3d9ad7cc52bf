# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-23 21:56-0800\n"
"PO-Revision-Date: 2020-05-23 00:56+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ua\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"
"X-Generator: Poedit 2.3.1\n"

#: registration/serializers.py:53
msgid "View is not defined, pass it as a context variable"
msgstr "View невідомий, передайте його як контекстну змінну"

#: registration/serializers.py:58
msgid "Define adapter_class in view"
msgstr "Встановіть adapter_class у view"

#: registration/serializers.py:77
msgid "Define callback_url in view"
msgstr "Встановіть callback_url у view"

#: registration/serializers.py:81
msgid "Define client_class in view"
msgstr "Встановіть client_class у view"

#: registration/serializers.py:102
msgid "Incorrect input. access_token or code is required."
msgstr "Некоректне введення. Необхідний access_token або код."

#: registration/serializers.py:111
msgid "Incorrect value"
msgstr "Некоректне значення"

#: registration/serializers.py:140
msgid "A user is already registered with this e-mail address."
msgstr "Користувач з такою e-mail адресою вже зареєстрований."

#: registration/serializers.py:148
msgid "The two password fields didn't match."
msgstr "Паролі не збігаються."

#: registration/views.py:44
msgid "Verification e-mail sent."
msgstr "Лист з підтвердженням вислано на email."

#: registration/views.py:91
msgid "ok"
msgstr "добре"

#: serializers.py:30
msgid "Must include \"email\" and \"password\"."
msgstr "Має включати \"email\" і \"пароль\"."

#: serializers.py:41
msgid "Must include \"username\" and \"password\"."
msgstr "Має включати \"юзернейм\" і \"пароль\"."

#: serializers.py:54
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr "Повинно включати або \"юзернейм\" або \"email\", і \"пароль\"."

#: serializers.py:95
msgid "User account is disabled."
msgstr "Користувач вимкнений."

#: serializers.py:98
msgid "Unable to log in with provided credentials."
msgstr "Неможливо увійти в систему із зазначеними обліковими даними."

#: serializers.py:107
msgid "E-mail is not verified."
msgstr "E-mail не підтверджений."

#: views.py:126
msgid "Successfully logged out."
msgstr "Успішний вихід."

#: views.py:174
msgid "Password reset e-mail has been sent."
msgstr "Лист з інструкціями по відновленню пароля вислано."

#: views.py:200
msgid "Password has been reset with the new password."
msgstr "Пароль змінено на новий."

#: views.py:222
msgid "New password has been saved."
msgstr "Новий пароль збережений."
