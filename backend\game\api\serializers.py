# ─────────────────────────── serializers ────────────────────────────
from rest_framework import serializers
import time
from ..models import (
    Attribute, Subskill, Perk,
    Chapter, Subchapter, Scene, GameObject, Transition,
    ActionMeta, ActionLog, SaveGame, GameEnd
)
from dj_rest_auth.registration.serializers import SocialLoginSerializer
from allauth.socialaccount.providers.oauth2.client import OAuth2Error
from rest_framework.exceptions import ValidationError
from django.core.exceptions import MultipleObjectsReturned
from game.utils.logging import GameLogger, log_validation_error


class AttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Attribute
        fields = ["code", "name"]


class SubskillSerializer(serializers.ModelSerializer):
    primary_attr = serializers.SlugRelatedField(slug_field="code", queryset=Attribute.objects.all())

    class Meta:
        model = Subskill
        fields = ["code", "name", "primary_attr"]


class PerkSerializer(serializers.ModelSerializer):
    class Meta:
        model = Perk
        fields = "__all__"


class ActionMetaSerializer(serializers.ModelSerializer):
    attribute = serializers.SlugRelatedField(
        slug_field="code",
        queryset=Attribute.objects.all(),
        allow_null=True,
        required=False
    )
    subskill = serializers.SlugRelatedField(
        slug_field="code",
        queryset=Subskill.objects.all(),
        allow_null=True,
        required=False
    )

    class Meta:
        model = ActionMeta
        fields = "__all__"
        # ✅ Dodaj diagram_json do serializowanych pól

    def create(self, validated_data):
        """Create ActionMeta with proper logging."""
        GameLogger.info(
            "Creating ActionMeta",
            action_id=validated_data.get('id'),
            action_type=validated_data.get('action_type'),
            attribute=validated_data.get('attribute').code if validated_data.get('attribute') else None
        )

        instance = super().create(validated_data)

        GameLogger.info(
            "ActionMeta created successfully",
            action_id=instance.id,
            attribute=instance.attribute.code if instance.attribute else None
        )
        return instance

    def validate_attribute(self, value):
        """Walidacja atrybutu - może być None dla non-skill actions."""
        return value

    def validate_available_skills(self, value):
        """Walidacja listy umiejętności."""
        if not isinstance(value, list):
            return []
        return value

    def validate(self, data):
        """Validate ActionMeta data with proper logging."""
        action_type = data.get('action_type', 'skill_test')

        # For skill_test, ensure required fields
        if action_type == 'skill_test':
            if not data.get('base_diff'):
                data['base_diff'] = 2  # Default difficulty

            # Check if attribute exists for skill_test
            attribute = data.get('attribute')
            if attribute is None:
                # Try to find DEX attribute as default
                try:
                    from ..models import Attribute
                    default_attr = Attribute.objects.get(code='DEX')
                    data['attribute'] = default_attr
                    GameLogger.info("Set default attribute DEX for skill_test")
                except Attribute.DoesNotExist:
                    GameLogger.warning("No DEX attribute found, skill_test will have no attribute")

        # For other action types, clear unnecessary fields
        elif action_type in ['scene_transition', 'story_choice']:
            data['base_diff'] = None
            data['attribute'] = None
            data['subskill'] = None
            data['available_skills'] = []
            data['primary_skill'] = ''
            data['allow_skill_choice'] = False

        return data


class GameObjectSerializer(serializers.ModelSerializer):
    scene = serializers.SlugRelatedField(slug_field="slug", queryset=Scene.objects.all())

    class Meta:
        model = GameObject
        fields = "__all__"


class SceneSerializer(serializers.ModelSerializer):
    objects = GameObjectSerializer(source="game_objects", many=True, read_only=True)
    actions = serializers.SerializerMethodField()
    executed_actions = serializers.SerializerMethodField()
    last_action_result = serializers.SerializerMethodField()
    subchapter_title = serializers.CharField(source='subchapter.title', read_only=True)
    chapter_title = serializers.CharField(source='subchapter.chapter.title', read_only=True)

    class Meta:
        model = Scene
        fields = [
            "slug",
            "title",
            "description",
            "mood",
            "subchapter",  # ✅ Dodajemy subchapter field
            "subchapter_title",  # Tytuł podrozdziału
            "chapter_title",  # Tytuł rozdziału
            "available_actions",
            "actions",
            "executed_actions",
            "last_action_result",
            "diagram_json",
            "entry_conditions",
            "exit_effects",
            "objects",
        ]
        extra_kwargs = {
            "actions": {"read_only": True},  # SerializerMethodField
            "executed_actions": {"read_only": True},  # SerializerMethodField
            "last_action_result": {"read_only": True},  # SerializerMethodField
            "objects": {"read_only": True},  # Related field
        }

    def validate_title(self, value):
        """Validate scene title."""
        if not value or not value.strip():
            log_validation_error('title', value, 'Title cannot be empty')
            raise serializers.ValidationError("Title cannot be empty")

        if len(value.strip()) > 120:
            log_validation_error('title', value, 'Title too long')
            raise serializers.ValidationError("Title cannot exceed 120 characters")

        return value.strip()

    def validate_description(self, value):
        """Validate scene description."""
        if value and len(value) > 5000:
            log_validation_error('description', value[:100] + '...', 'Description too long')
            raise serializers.ValidationError("Description cannot exceed 5000 characters")

        return value

    def validate_slug(self, value):
        """Validate scene slug."""
        if value:
            import re
            if not re.match(r'^[a-z0-9-]+$', value):
                log_validation_error('slug', value, 'Invalid slug format')
                raise serializers.ValidationError("Slug can only contain lowercase letters, numbers, and hyphens")

        return value

    def create(self, validated_data):
        """Create scene with auto-generated slug if not provided."""
        if 'slug' not in validated_data or not validated_data['slug']:
            # Generate slug from title
            title = validated_data.get('title', 'scene')
            base_slug = self._generate_slug_from_title(title)
            validated_data['slug'] = self._ensure_unique_slug(base_slug)

        GameLogger.info(
            "Creating scene",
            title=validated_data.get('title'),
            slug=validated_data.get('slug')
        )

        return super().create(validated_data)

    def _generate_slug_from_title(self, title):
        """Generate slug from title."""
        import re
        slug = title.lower()
        slug = re.sub(r'[^a-z0-9\s-]', '', slug)  # Remove special chars
        slug = re.sub(r'\s+', '-', slug)  # Replace spaces with hyphens
        slug = re.sub(r'-+', '-', slug)  # Replace multiple hyphens
        slug = slug.strip('-')  # Remove leading/trailing hyphens
        return slug or f'scene-{int(time.time())}'

    def _ensure_unique_slug(self, base_slug):
        """Ensure slug is unique by adding number suffix if needed."""
        import time
        from django.db import models

        slug = base_slug
        counter = 1

        while Scene.objects.filter(slug=slug).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1

            # Fallback to timestamp if too many attempts
            if counter > 100:
                slug = f"{base_slug}-{int(time.time())}"
                break

        return slug

    def get_actions(self, obj):
        """Zwraca pełne dane akcji dostępnych w tej scenie."""
        actions = obj.get_available_actions()
        return ActionMetaSerializer(actions, many=True).data

    def get_executed_actions(self, obj):
        """Zwraca listę ID akcji już wykonanych w tej scenie przez aktualnego gracza."""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return []

        from game.logic.xp import get_player_save_data
        save_data = get_player_save_data(request.user)
        executed_actions = save_data.get("executed_actions", {})
        return executed_actions.get(obj.slug, [])

    def get_last_action_result(self, obj):
        """Zwraca wynik ostatniej akcji w tej scenie (dla refresh recovery)."""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None

        from game.logic.xp import get_player_save_data
        save_data = get_player_save_data(request.user)
        last_result = save_data.get("last_action_result")

        # Return only if it's for current scene
        if last_result and last_result.get("scene_slug") == obj.slug:
            return last_result

        return None


class TransitionSerializer(serializers.ModelSerializer):
    origin = serializers.SlugRelatedField(slug_field="slug", queryset=Scene.objects.all())
    target = serializers.SlugRelatedField(slug_field="slug", queryset=Scene.objects.all(), required=False, allow_null=True)
    target_chapter = serializers.PrimaryKeyRelatedField(queryset=Chapter.objects.all(), required=False, allow_null=True)
    target_subchapter = serializers.PrimaryKeyRelatedField(queryset=Subchapter.objects.all(), required=False, allow_null=True)

    # Read-only fields for display
    target_display = serializers.CharField(source='get_target_display', read_only=True)
    target_chapter_title = serializers.CharField(source='target_chapter.title', read_only=True)
    target_subchapter_title = serializers.CharField(source='target_subchapter.title', read_only=True)

    class Meta:
        model = Transition
        fields = [
            "id", "origin", "transition_type",
            "target", "target_chapter", "target_subchapter",
            "target_display", "target_chapter_title", "target_subchapter_title",
            "condition_expr", "description", "auto"
        ]

    def validate(self, data):
        """Walidacja - dokładnie jeden cel musi być ustawiony."""
        transition_type = data.get('transition_type', 'scene')
        target = data.get('target')
        target_chapter = data.get('target_chapter')
        target_subchapter = data.get('target_subchapter')

        targets = [target, target_chapter, target_subchapter]
        filled_targets = [t for t in targets if t is not None]

        if len(filled_targets) != 1:
            raise serializers.ValidationError("Dokładnie jeden cel przejścia musi być ustawiony.")

        # Sprawdź zgodność typu z celem
        if transition_type == 'scene' and not target:
            raise serializers.ValidationError("Dla typu 'scene' wymagana jest scena docelowa.")
        elif transition_type == 'chapter' and not target_chapter:
            raise serializers.ValidationError("Dla typu 'chapter' wymagany jest rozdział docelowy.")
        elif transition_type == 'subchapter' and not target_subchapter:
            raise serializers.ValidationError("Dla typu 'subchapter' wymagany jest podrozdział docelowy.")

        return data





class ActionLogSerializer(serializers.ModelSerializer):
    scene = serializers.SlugRelatedField(slug_field="slug", read_only=True)
    action = serializers.SlugRelatedField(slug_field="id", read_only=True)

    class Meta:
        model = ActionLog
        fields = ["id", "scene", "action", "outcome", "roll_value", "target_value", "timestamp"]

class SaveGameSerializer(serializers.Serializer):
    slot = serializers.IntegerField(min_value=0, max_value=3, default=0)
    data = serializers.JSONField()

    def create(self, validated_data):
        SaveGame.save_for(
            self.context["request"].user,
            validated_data["data"],
            slot=validated_data["slot"],
        )
        return {
            "slot": validated_data["slot"],
            "data": validated_data["data"],
            "saved": True
        }

    def to_representation(self, instance):
        # instance może być listą SaveGame, pojedynczym SaveGame lub dict-em
        if isinstance(instance, list):
            # Lista SaveGame obiektów (GET /saves/)
            return [
                {
                    "slot": sg.slot,
                    "updated_at": sg.updated_at,
                    "size": len(sg.data_gzip),
                }
                for sg in instance
            ]
        elif hasattr(instance, 'slot'):
            # Pojedynczy SaveGame obiekt
            return {
                "slot": instance.slot,
                "updated_at": instance.updated_at,
                "size": len(instance.data_gzip),
            }
        else:
            # Dict z danymi (POST response)
            return instance
    
class SafeSocialLoginSerializer(SocialLoginSerializer):
    def validate(self, attrs):
        try:
            return super().validate(attrs)

        # token nieprawidłowy ➔ 400
        except OAuth2Error as e:
            raise ValidationError({"detail": "Invalid or expired token"}) from e

        # zduplikowany SocialApp ➔ 400 (konfig. serwera)
        except MultipleObjectsReturned as e:
            raise ValidationError(
                {"detail": "Mis-configured provider (duplicate SocialApp)"}
            ) from e


class ChapterSerializer(serializers.ModelSerializer):
    subchapters_count = serializers.SerializerMethodField()

    class Meta:
        model = Chapter
        fields = ['id', 'title', 'description', 'order', 'is_active', 'subchapters_count']

    def get_subchapters_count(self, obj):
        return obj.subchapters.filter(is_active=True).count()


class SubchapterSerializer(serializers.ModelSerializer):
    scenes_count = serializers.SerializerMethodField()
    chapter_title = serializers.CharField(source='chapter.title', read_only=True)

    class Meta:
        model = Subchapter
        fields = ['id', 'chapter', 'chapter_title', 'title', 'description', 'order', 'is_active', 'scenes_count']

    def get_scenes_count(self, obj):
        return obj.scenes.count()


class ChapterDetailSerializer(serializers.ModelSerializer):
    subchapters = SubchapterSerializer(many=True, read_only=True)

    class Meta:
        model = Chapter
        fields = ['id', 'title', 'description', 'order', 'is_active', 'subchapters']


class GameEndSerializer(serializers.ModelSerializer):
    class Meta:
        model = GameEnd
        fields = ['id', 'title', 'end_type', 'message', 'is_active']


class SubchapterDetailSerializer(serializers.ModelSerializer):
    scenes = serializers.SerializerMethodField()
    chapter_title = serializers.CharField(source='chapter.title', read_only=True)

    class Meta:
        model = Subchapter
        fields = ['id', 'chapter', 'chapter_title', 'title', 'description', 'order', 'is_active', 'scenes']

    def get_scenes(self, obj):
        scenes = obj.scenes.all()
        return SceneSerializer(scenes, many=True).data