# Generated by Django 5.2.1 on 2025-06-19 17:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0008_actionmeta_allow_skill_choice_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='actionmeta',
            name='action_type',
            field=models.CharField(choices=[('skill_test', 'Test umiejętności'), ('scene_transition', 'Przejście do sceny'), ('dialogue_continue', 'Kontynuacja dialogu'), ('story_choice', 'Wybór fabularny')], default='skill_test', help_text='Typ akcji - test umiejętności lub przejście/wybór fabularny', max_length=20),
        ),
        migrations.AddField(
            model_name='actionmeta',
            name='target_scene',
            field=models.CharField(blank=True, help_text="Slug sceny docelowej dla prz<PERSON> (tylko dla action_type='scene_transition')", max_length=100),
        ),
        migrations.AddField(
            model_name='scene',
            name='available_actions',
            field=models.JSONField(blank=True, default=list, help_text="Lista ID akcji dostępnych w tej scenie (np. ['zombie_run_away', 'zombie_talk'])"),
        ),
        migrations.AlterField(
            model_name='actionmeta',
            name='attribute',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='game.attribute'),
        ),
        migrations.AlterField(
            model_name='actionmeta',
            name='base_diff',
            field=models.PositiveSmallIntegerField(blank=True, null=True),
        ),
    ]
