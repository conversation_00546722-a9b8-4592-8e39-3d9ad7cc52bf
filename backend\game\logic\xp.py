"""
game.logic.xp
~~~~~~~~~~~~~

XP growth system ported from main.js
- Attribute and subskill XP progression
- Level thresholds and advancement
- Main XP and level up system
"""

from typing import Dict, Any, List
from django.contrib.auth import get_user_model

from ..models import ActionMeta, SaveGame

User = get_user_model()

# Progressive XP thresholds for attributes (level 1 needs 5, level 2 needs 7, level 3 needs 10, etc.)
def get_attribute_threshold(level):
    """Get XP threshold for next attribute level."""
    if level <= 0:
        return 5
    elif level == 1:
        return 7
    elif level == 2:
        return 10
    else:
        # Progressive scaling: 10 + (level-2) * 3
        return 10 + (level - 2) * 3

# XP thresholds for subskills (simpler progression)
SUBSKILL_XP_THRESHOLDS = [0] + [10 + (i - 1) * 2 for i in range(1, 21)]

# Main XP thresholds for character levels
MAIN_XP_THRESHOLDS = [0, 50, 75, 100, 125, 150, 175, 200, 225, 250]

# Main XP multiplier per action
MAIN_XP_MULTIPLIER = 5

# Attribute relationships - chance to gain points when using primary attribute
ATTRIBUTE_RELATIONSHIPS = {
    "STR": {
        "STR": 75,  # Primary attribute
        "DEX": 30,  # Physical coordination
        "TEC": 10,  # Some overlap with crafting
        "PER": 15,  # Body awareness
        "WIL": 40,  # Mental fortitude
        "CHA": 20,  # Physical presence
    },
    "DEX": {
        "STR": 30,  # Physical coordination
        "DEX": 75,  # Primary attribute
        "TEC": 40,  # Fine motor skills
        "PER": 50,  # Hand-eye coordination
        "WIL": 25,  # Focus and precision
        "CHA": 15,  # Grace and poise
    },
    "TEC": {
        "STR": 10,  # Some crafting overlap
        "DEX": 40,  # Fine motor skills
        "TEC": 75,  # Primary attribute
        "PER": 45,  # Attention to detail
        "WIL": 40,  # Problem solving
        "CHA": 20,  # Communication of ideas
    },
    "PER": {
        "STR": 15,  # Body awareness
        "DEX": 50,  # Hand-eye coordination
        "TEC": 45,  # Attention to detail
        "PER": 75,  # Primary attribute
        "WIL": 35,  # Mental focus
        "CHA": 30,  # Reading people
    },
    "WIL": {
        "STR": 40,  # Mental fortitude
        "DEX": 25,  # Focus and precision
        "TEC": 40,  # Problem solving
        "PER": 35,  # Mental focus
        "WIL": 75,  # Primary attribute
        "CHA": 45,  # Force of personality
    },
    "CHA": {
        "STR": 20,  # Physical presence
        "DEX": 15,  # Grace and poise
        "TEC": 20,  # Communication of ideas
        "PER": 30,  # Reading people
        "WIL": 45,  # Force of personality
        "CHA": 75,  # Primary attribute
    },
}

# Attribute to skills mapping - which skills can be improved when attribute levels up
ATTRIBUTE_SKILL_MAPPING = {
    "STR": ["melee", "athletics", "intimidation", "demolitions"],
    "DEX": ["firearms", "stealth", "lockpicking", "driving", "athletics"],  # athletics overlaps
    "TEC": ["hacking", "electronics", "crafting", "repair", "demolitions", "lockpicking"],  # overlaps
    "PER": ["observation", "investigation", "tracking", "stealth", "firearms"],  # overlaps
    "WIL": ["concentration", "resistance", "intimidation", "persuasion"],  # overlaps
    "CHA": ["persuasion", "deception", "leadership", "intimidation", "investigation"],  # overlaps
}

# Skill names for display
SKILL_NAMES = {
    'melee': 'Walka wręcz',
    'athletics': 'Atletyka',
    'intimidation': 'Zastraszanie',
    'demolitions': 'Materiały wybuchowe',
    'firearms': 'Broń palna',
    'stealth': 'Skradanie',
    'lockpicking': 'Włamywanie',
    'driving': 'Prowadzenie',
    'hacking': 'Hacking',
    'electronics': 'Elektronika',
    'crafting': 'Rzemiosło',
    'repair': 'Naprawa',
    'observation': 'Obserwacja',
    'investigation': 'Śledztwo',
    'tracking': 'Tropienie',
    'concentration': 'Koncentracja',
    'resistance': 'Odporność',
    'persuasion': 'Perswazja',
    'deception': 'Oszustwo',
    'leadership': 'Przywództwo',
}


def get_first_scene_slug() -> str:
    """Get the slug of the first scene from the first subchapter of the first chapter."""
    from ..models import Chapter, Scene

    try:
        print("🔍 Looking for first scene...")

        # Find first active chapter
        first_chapter = Chapter.objects.filter(is_active=True).order_by('order').first()
        if not first_chapter:
            print("⚠️ No active chapters found")
            # Try to find any scene as fallback
            any_scene = Scene.objects.filter(subchapter__isnull=False).first()
            if any_scene:
                print(f"🔄 Using fallback scene: {any_scene.slug}")
                return any_scene.slug
            return "default-scene"  # Ultimate fallback

        print(f"📖 Found first chapter: {first_chapter.title}")

        # Find first active subchapter in that chapter
        first_subchapter = first_chapter.subchapters.filter(is_active=True).order_by('order').first()
        if not first_subchapter:
            print(f"⚠️ No active subchapters in chapter {first_chapter.title}")
            # Try to find any scene as fallback
            any_scene = Scene.objects.filter(subchapter__isnull=False).first()
            if any_scene:
                print(f"🔄 Using fallback scene: {any_scene.slug}")
                return any_scene.slug
            return "default-scene"  # Ultimate fallback

        print(f"📄 Found first subchapter: {first_subchapter.title}")

        # Find first scene in that subchapter
        first_scene = first_subchapter.scenes.filter(is_active=True).order_by('id').first()
        if not first_scene:
            print(f"⚠️ No scenes in subchapter {first_subchapter.title}")
            # Try to find any scene as fallback
            any_scene = Scene.objects.filter(subchapter__isnull=False).first()
            if any_scene:
                print(f"🔄 Using fallback scene: {any_scene.slug}")
                return any_scene.slug
            return "default-scene"  # Ultimate fallback

        print(f"🎬 Found first scene: {first_scene.slug}")
        return first_scene.slug

    except Exception as e:
        print(f"❌ Error finding first scene: {e}")
        # Try to find any scene as fallback
        try:
            from ..models import Scene
            any_scene = Scene.objects.filter(subchapter__isnull=False).first()
            if any_scene:
                print(f"🔄 Using fallback scene: {any_scene.slug}")
                return any_scene.slug
        except:
            pass
        return "default-scene"  # Ultimate fallback


def get_player_save_data(user: User) -> Dict[str, Any]:
    """Get player's complete save data or create default."""
    save_data = SaveGame.load_for(user, slot=0)
    if not save_data:
        return create_default_save_data()
    return save_data


def create_default_save_data() -> Dict[str, Any]:
    """Create default save data for new player."""
    # Find the first scene from the first subchapter of the first chapter
    starting_scene = get_first_scene_slug()

    return {
        "level": 1,
        "main_xp": 0,
        "can_level_up": False,
        "chosen_perks": [],
        "attributes": {
            "STR": {"value": 1, "xp": 0},
            "DEX": {"value": 1, "xp": 0},
            "TEC": {"value": 1, "xp": 0},
            "PER": {"value": 1, "xp": 0},
            "WIL": {"value": 1, "xp": 0},
            "CHA": {"value": 1, "xp": 0},
        },
        "subskills": {
            # STR skills
            "melee": {"value": 0, "xp": 0},
            "athletics": {"value": 0, "xp": 0},
            "intimidation": {"value": 0, "xp": 0},
            "demolitions": {"value": 0, "xp": 0},

            # DEX skills
            "firearms": {"value": 0, "xp": 0},
            "stealth": {"value": 0, "xp": 0},
            "lockpicking": {"value": 0, "xp": 0},
            "driving": {"value": 0, "xp": 0},

            # TEC skills
            "hacking": {"value": 0, "xp": 0},
            "electronics": {"value": 0, "xp": 0},
            "crafting": {"value": 0, "xp": 0},
            "repair": {"value": 0, "xp": 0},

            # PER skills
            "observation": {"value": 0, "xp": 0},
            "investigation": {"value": 0, "xp": 0},
            "tracking": {"value": 0, "xp": 0},

            # WIL skills
            "concentration": {"value": 0, "xp": 0},
            "resistance": {"value": 0, "xp": 0},

            # CHA skills
            "persuasion": {"value": 0, "xp": 0},
            "deception": {"value": 0, "xp": 0},
            "leadership": {"value": 0, "xp": 0},
        },
        "gameplay_tags": {},
        "scene": starting_scene,  # Starting scene from first subchapter of first chapter
        "executed_actions": {},  # Track executed actions per scene: {"scene_slug": ["action_id1", "action_id2"]}
        "current_scene_session": None,  # Track current scene session to prevent refresh farming
        "last_action_result": None,  # Store last action result for refresh recovery
    }


def add_xp_to_attribute(attr_data: Dict[str, Any], gained_points: int) -> bool:
    """
    Add XP points to an attribute and handle level up.

    Returns:
        bool: True if level increased, False otherwise
    """
    attr_data["xp"] += gained_points
    current_level = attr_data["value"]

    # Get threshold for next level
    next_threshold = get_attribute_threshold(current_level)

    # Check if we can level up
    if attr_data["xp"] >= next_threshold:
        attr_data["xp"] -= next_threshold
        attr_data["value"] += 1
        return True

    return False


def add_xp_to_subskill(skill_data: Dict[str, Any], gained_xp: int) -> bool:
    """
    Add XP to a subskill and handle level up.

    Returns:
        bool: True if level increased, False otherwise
    """
    skill_data["xp"] += gained_xp
    current_level = skill_data["value"]

    # Check if we can level up (using old system for subskills)
    if current_level < len(SUBSKILL_XP_THRESHOLDS) - 1:
        next_threshold = SUBSKILL_XP_THRESHOLDS[current_level + 1]
        if skill_data["xp"] >= next_threshold:
            skill_data["xp"] -= next_threshold
            skill_data["value"] += 1
            return True

    return False


def roll_attribute_points(primary_attribute: str, multiplier: float, chosen_perks: List[str] = None) -> Dict[str, int]:
    """
    Roll for attribute points based on relationships and outcome multiplier.
    Enhanced with perk bonuses for attribute growth.

    Args:
        primary_attribute: The main attribute used in the action
        multiplier: Outcome multiplier (0.5 for fail, 1.5 for critical, etc.)
        chosen_perks: List of player's chosen perks

    Returns:
        Dict with attribute codes and points gained
    """
    import random
    from ..models import Perk

    points_gained = {}

    if primary_attribute not in ATTRIBUTE_RELATIONSHIPS:
        return points_gained

    relationships = ATTRIBUTE_RELATIONSHIPS[primary_attribute]

    # Get attribute growth bonuses from perks
    attr_growth_bonuses = {}
    if chosen_perks:
        player_perk_objects = Perk.objects.filter(id__in=chosen_perks)
        for perk in player_perk_objects:
            if perk.effect_type == 'attr_growth':
                for attr_tag in perk.effect_tags:
                    if attr_tag.upper() in relationships:
                        # Add percentage bonus to growth chance
                        attr_growth_bonuses[attr_tag.upper()] = attr_growth_bonuses.get(attr_tag.upper(), 0) + perk.effect_value

    for attr_code, base_chance in relationships.items():
        # Apply outcome multiplier to chance
        adjusted_chance = base_chance * multiplier

        # Apply perk bonuses
        if attr_code in attr_growth_bonuses:
            perk_bonus = attr_growth_bonuses[attr_code]
            adjusted_chance = adjusted_chance * (1 + perk_bonus / 100)

        # Cap at 95%
        adjusted_chance = min(95, adjusted_chance)

        # Roll for point
        if random.randint(1, 100) <= adjusted_chance:
            points_gained[attr_code] = points_gained.get(attr_code, 0) + 1

    return points_gained


def apply_attribute_growth(save_data: Dict[str, Any], primary_attribute: str, multiplier: float, chosen_perks: List[str] = None):
    """Apply attribute point growth based on relationships."""
    level_ups = []

    # Roll for attribute points with perk bonuses
    points_gained = roll_attribute_points(primary_attribute, multiplier, chosen_perks)

    # Apply points to attributes
    for attr_code, points in points_gained.items():
        if attr_code in save_data["attributes"]:
            for _ in range(points):
                add_xp_to_attribute(save_data["attributes"][attr_code], 1)
                # Level up notifications are handled in frontend

    return level_ups, points_gained


def get_available_skills_for_attribute(attribute: str) -> List[str]:
    """Get list of skills that can be improved when an attribute levels up."""
    return ATTRIBUTE_SKILL_MAPPING.get(attribute, [])


def apply_subskill_growth(save_data: Dict[str, Any], skill_growth: Dict[str, int], multiplier: float):
    """Apply XP growth to subskills."""
    level_ups = []

    for skill_code, base_xp in skill_growth.items():
        if skill_code in save_data["subskills"]:
            gained_xp = int(base_xp * multiplier)
            if add_xp_to_subskill(save_data["subskills"][skill_code], gained_xp):
                level_ups.append(f"{skill_code} increased to {save_data['subskills'][skill_code]['value']}")

    return level_ups


def apply_main_xp(save_data: Dict[str, Any], multiplier: float, chosen_perks: List[str] = None) -> bool:
    """
    Apply main XP gain and check for level up.
    Enhanced with perk bonuses for main XP growth.

    Returns:
        bool: True if can level up, False otherwise
    """
    from ..models import Perk

    base_main_xp = int(MAIN_XP_MULTIPLIER * multiplier)

    # Calculate main XP bonuses from perks
    main_xp_bonus = 0
    if chosen_perks:
        player_perk_objects = Perk.objects.filter(id__in=chosen_perks)
        for perk in player_perk_objects:
            if perk.effect_type == 'main_xp_bonus':
                main_xp_bonus += perk.effect_value

    # Apply bonus
    gained_main_xp = base_main_xp
    if main_xp_bonus > 0:
        gained_main_xp = int(base_main_xp * (1 + main_xp_bonus / 100))

    save_data["main_xp"] += gained_main_xp

    current_level = save_data["level"]
    if current_level < len(MAIN_XP_THRESHOLDS):
        threshold = MAIN_XP_THRESHOLDS[current_level]
        if save_data["main_xp"] >= threshold and not save_data["can_level_up"]:
            save_data["can_level_up"] = True
            return True

    return False


def apply_xp_growth(user: User, action_meta: ActionMeta, outcome: str) -> Dict[str, Any]:
    """
    Apply XP growth from action outcome.

    Returns:
        Dict with growth details and level ups
    """
    from .dice import OUTCOME_MULTIPLIERS

    # Get multiplier for outcome
    multiplier = OUTCOME_MULTIPLIERS.get(outcome, 1.0)

    # Get current save data
    save_data = get_player_save_data(user)

    # Track changes
    changes = {
        "attribute_level_ups": [],
        "attribute_points_gained": {},
        "subskill_level_ups": [],
        "main_xp_gained": int(MAIN_XP_MULTIPLIER * multiplier),
        "can_level_up": False,
        "multiplier": multiplier,
    }

    # Get player's chosen perks
    chosen_perks = save_data.get("chosen_perks", [])

    # Apply attribute growth (new system) with perk bonuses
    primary_attr = action_meta.attribute.code
    attr_level_ups, attr_points = apply_attribute_growth(
        save_data, primary_attr, multiplier, chosen_perks
    )
    changes["attribute_level_ups"] = attr_level_ups
    changes["attribute_points_gained"] = attr_points

    # NO MORE AUTOMATIC SUBSKILL GROWTH
    # Subskills are now only improved manually when attributes level up
    changes["subskill_level_ups"] = []

    # Apply main XP with perk bonuses
    changes["can_level_up"] = apply_main_xp(save_data, multiplier, chosen_perks)

    # Save updated data
    SaveGame.save_for(user, save_data, slot=0)

    return changes


def level_up_character(user: User, chosen_perk_id: str = None) -> Dict[str, Any]:
    """
    Level up character and optionally choose a perk.
    
    Returns:
        Dict with level up results
    """
    save_data = get_player_save_data(user)
    
    if not save_data["can_level_up"]:
        return {"success": False, "error": "Cannot level up yet"}
    
    # Increase level
    save_data["level"] += 1
    save_data["can_level_up"] = False
    
    # Add chosen perk if provided
    if chosen_perk_id and chosen_perk_id not in save_data["chosen_perks"]:
        save_data["chosen_perks"].append(chosen_perk_id)
    
    # Save updated data
    SaveGame.save_for(user, save_data, slot=0)
    
    return {
        "success": True,
        "new_level": save_data["level"],
        "chosen_perk": chosen_perk_id,
    }


def get_attribute_progress_info(attr_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get progress information for an attribute (for UI progress bars).

    Returns:
        Dict with current_xp, next_threshold, progress_percent
    """
    current_level = attr_data["value"]
    current_xp = attr_data["xp"]
    next_threshold = get_attribute_threshold(current_level)

    progress_percent = min(100, (current_xp / next_threshold) * 100) if next_threshold > 0 else 100

    return {
        "current_xp": current_xp,
        "next_threshold": next_threshold,
        "progress_percent": round(progress_percent, 1),
        "current_level": current_level,
    }


def is_action_executed(user, scene_slug: str, action_id: str) -> bool:
    """Check if action has already been executed in this scene."""
    save_data = get_player_save_data(user)
    executed_actions = save_data.get("executed_actions", {})
    scene_actions = executed_actions.get(scene_slug, [])
    return action_id in scene_actions


def mark_action_executed(user, scene_slug: str, action_id: str, action_type: str = None):
    """Mark action as executed in this scene. Only skill_test actions are tracked."""
    # Only track skill tests, not scene transitions or dialogue
    if action_type not in ['skill_test']:
        return

    save_data = get_player_save_data(user)

    # Initialize executed_actions if not present (for old saves)
    if "executed_actions" not in save_data:
        save_data["executed_actions"] = {}

    # Initialize scene list if not present
    if scene_slug not in save_data["executed_actions"]:
        save_data["executed_actions"][scene_slug] = []

    # Add action if not already present
    if action_id not in save_data["executed_actions"][scene_slug]:
        save_data["executed_actions"][scene_slug].append(action_id)

    # Save updated data
    from ..models import SaveGame
    SaveGame.save_for(user, save_data, slot=0)


def reset_scene_actions_if_new_visit(user, scene_slug: str):
    """Reset executed actions for a scene only if this is a new visit (not refresh)."""
    save_data = get_player_save_data(user)

    # Initialize fields if not present (for old saves)
    if "executed_actions" not in save_data:
        save_data["executed_actions"] = {}
    if "current_scene_session" not in save_data:
        save_data["current_scene_session"] = None

    # Check if this is a new scene visit
    current_session = save_data.get("current_scene_session")

    # If we're in a different scene than last time, reset actions for new scene
    if current_session != scene_slug:
        # Clear executed actions for this scene (new visit)
        save_data["executed_actions"][scene_slug] = []
        # Clear last action result (new visit)
        save_data["last_action_result"] = None
        # Update current session
        save_data["current_scene_session"] = scene_slug

        # Save updated data
        from ..models import SaveGame
        SaveGame.save_for(user, save_data, slot=0)
        return True  # New visit

    return False  # Same session (refresh)
