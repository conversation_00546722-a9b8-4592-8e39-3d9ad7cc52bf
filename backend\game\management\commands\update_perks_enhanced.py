"""
Management command to update perks with enhanced effect system.

Usage:
    python manage.py update_perks_enhanced

This command will:
1. Parse existing perk descriptions to determine effect types
2. Update perks with proper effect_type, effect_value, and effect_tags
3. Create a more structured perk system
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from game.models import Perk
import re


class Command(BaseCommand):
    help = "Update perks with enhanced effect system"

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without actually updating',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be made"))
        
        # Enhanced perk definitions with proper categorization
        enhanced_perks = {
            # ROLL BONUS PERKS - Bonusy do rzutów
            "quick_reflexes": {
                "effect_type": "roll_bonus",
                "effect_value": 10,
                "effect_tags": ["dex", "evasion"]
            },
            "wrestler": {
                "effect_type": "roll_bonus", 
                "effect_value": 20,
                "effect_tags": ["str", "melee"]
            },
            "clever_bluff": {
                "effect_type": "roll_bonus",
                "effect_value": 15,
                "effect_tags": ["cha", "dialogue"]
            },
            "sniper_instinct": {
                "effect_type": "roll_bonus",
                "effect_value": 10,
                "effect_tags": ["dex", "firearms"]
            },
            "amateur_assassin": {
                "effect_type": "roll_bonus",
                "effect_value": 15,
                "effect_tags": ["dex", "stealth"]
            },
            "combat_readiness": {
                "effect_type": "roll_bonus",
                "effect_value": 5,
                "effect_tags": ["str", "dex"]
            },
            "parkour_master": {
                "effect_type": "roll_bonus",
                "effect_value": 20,
                "effect_tags": ["dex", "evasion"]
            },
            "terrain_sense": {
                "effect_type": "roll_bonus",
                "effect_value": 25,
                "effect_tags": ["per", "analysis"]
            },
            "interrogator": {
                "effect_type": "roll_bonus",
                "effect_value": 15,
                "effect_tags": ["cha", "dialogue"]
            },
            "improvisation_expert": {
                "effect_type": "roll_bonus",
                "effect_value": 10,
                "effect_tags": ["wil", "tec"]
            },
            "intimidating_presence": {
                "effect_type": "roll_bonus",
                "effect_value": 15,
                "effect_tags": ["str", "cha"]
            },
            "silent_steps": {
                "effect_type": "roll_bonus",
                "effect_value": 30,
                "effect_tags": ["dex", "stealth"]
            },
            "close_quarters_expert": {
                "effect_type": "roll_bonus",
                "effect_value": 10,
                "effect_tags": ["melee", "firearms"]
            },
            "cipher_master": {
                "effect_type": "roll_bonus",
                "effect_value": 25,
                "effect_tags": ["tec", "hacking"]
            },
            "magnetic_personality": {
                "effect_type": "roll_bonus",
                "effect_value": 15,
                "effect_tags": ["cha", "dialogue"]
            },
            "unbreakable": {
                "effect_type": "roll_bonus",
                "effect_value": 25,
                "effect_tags": ["wil", "psyche"]
            },

            # ATTRIBUTE GROWTH PERKS - Szybszy rozwój atrybutów
            "technical_touch": {
                "effect_type": "attr_growth",
                "effect_value": 100,  # 2x = +100%
                "effect_tags": ["tec"]
            },
            "data_collector": {
                "effect_type": "attr_growth",
                "effect_value": 50,
                "effect_tags": ["per", "tec"]
            },
            "extreme_training": {
                "effect_type": "attr_growth",
                "effect_value": 10,
                "effect_tags": ["str", "dex", "tec"]
            },
            "data_assimilation": {
                "effect_type": "attr_growth",
                "effect_value": 50,
                "effect_tags": ["wil"]
            },
            "super_consolidation": {
                "effect_type": "attr_growth",
                "effect_value": 25,
                "effect_tags": ["str", "dex", "tec", "cha", "per", "wil"]
            },

            # MAIN XP BONUS PERKS - Szybszy rozwój postaci
            "photographic_memory": {
                "effect_type": "main_xp_bonus",
                "effect_value": 25,
                "effect_tags": []
            },

            # SPECIAL PERKS - Unikalne efekty
            "calm_mind": {
                "effect_type": "special",
                "effect_value": 1,
                "effect_tags": ["wil"],
                "special_config": {"type": "first_test_success", "attribute": "wil"}
            },
            "tactical_foresight": {
                "effect_type": "special",
                "effect_value": 1,
                "effect_tags": ["dex", "analysis"],
                "special_config": {"type": "preview_results", "attribute": "dex"}
            },
            "lie_master": {
                "effect_type": "special",
                "effect_value": 1,
                "effect_tags": ["cha", "dialogue"],
                "special_config": {"type": "force_success", "per_scene": True}
            },
            "lucky_shot": {
                "effect_type": "special",
                "effect_value": 5,
                "effect_tags": ["dex", "firearms"],
                "special_config": {"type": "elimination_chance", "percentage": 5}
            },
            "backup_plan": {
                "effect_type": "special",
                "effect_value": 1,
                "effect_tags": ["wil", "analysis"],
                "special_config": {"type": "undo_choice", "per_missions": 2}
            },
            "auto_response": {
                "effect_type": "special",
                "effect_value": 1,
                "effect_tags": ["psyche", "evasion"],
                "special_config": {"type": "first_test_success", "per_scene": True}
            },
            "great_intuition": {
                "effect_type": "special",
                "effect_value": 1,
                "effect_tags": ["analysis", "per"],
                "special_config": {"type": "show_probability"}
            },

            # UNLOCK ACTIONS PERKS - Nowe akcje
            "camera_buster": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["tec", "stealth"]
            },
            "black_bag": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["str", "cha"]
            },
            "logical_override": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["wil", "ai"]
            },
            "tech_commando": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["tec", "stealth"]
            },
            "instinct_shot": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["dex", "firearms"]
            },
            "observer": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["per", "analysis"]
            },
            "sleeve_trick": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["cha", "dialogue"]
            },
            "logic_killer": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["ai", "psyche"]
            },
            "mimic_master": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["cha", "stealth"]
            },
            "preventive_action": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["per", "wil"]
            },
            "radio_silence": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["stealth", "ai"]
            },
            "precision_strike": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["melee", "firearms"]
            },
            "logic_breaker": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["ai", "psyche"]
            },
            "world_hack": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["tec", "ai"]
            },
            "silent_before_storm": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["final", "psyche"]
            },
            "mental_domination": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["wil", "ai"]
            },
            "destruction_machine": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["str", "tec"]
            },
            "chaotic_pattern": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["ai", "stealth"]
            },
            "ai_sense": {
                "effect_type": "unlock_actions",
                "effect_value": 1,
                "effect_tags": ["per", "ai"]
            },

            # Remaining perks with default values
            "data_noise": {
                "effect_type": "special",
                "effect_value": 1,
                "effect_tags": ["tec", "ai"],
                "special_config": {"type": "extra_story_clues"}
            },
            "iron_loyalty": {
                "effect_type": "special",
                "effect_value": 1,
                "effect_tags": ["cha", "dialogue"],
                "special_config": {"type": "reduce_betrayal"}
            },
        }
        
        updated_count = 0
        
        with transaction.atomic():
            for perk_id, config in enhanced_perks.items():
                try:
                    perk = Perk.objects.get(id=perk_id)
                    
                    if dry_run:
                        self.stdout.write(f"Would update {perk_id}:")
                        self.stdout.write(f"  Type: {config['effect_type']}")
                        self.stdout.write(f"  Value: {config['effect_value']}")
                        self.stdout.write(f"  Tags: {config['effect_tags']}")
                        if 'special_config' in config:
                            self.stdout.write(f"  Special: {config['special_config']}")
                    else:
                        perk.effect_type = config['effect_type']
                        perk.effect_value = config['effect_value']
                        perk.effect_tags = config['effect_tags']
                        if 'special_config' in config:
                            perk.special_config = config['special_config']
                        perk.save()
                        
                        self.stdout.write(
                            self.style.SUCCESS(f"Updated {perk_id} -> {config['effect_type']}")
                        )
                    
                    updated_count += 1
                    
                except Perk.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f"Perk {perk_id} not found in database")
                    )
        
        # Summary
        self.stdout.write("\n" + "="*50)
        self.stdout.write(f"SUMMARY:")
        self.stdout.write(f"Updated: {updated_count} perks")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("\nThis was a DRY RUN - no actual changes were made"))
            self.stdout.write("Run without --dry-run to apply changes")
        else:
            self.stdout.write(self.style.SUCCESS(f"\nSuccessfully updated {updated_count} perks!"))
