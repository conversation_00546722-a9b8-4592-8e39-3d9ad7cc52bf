# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-17 09:59+0100\n"
"PO-Revision-Date: 2023-03-17 10:21+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.2.2\n"

#: dj_rest_auth/app_settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Le paramètre '{}' a été supprimé. Veuillez vous référer à '{}' pour les "
"paramètres disponibles."

#: dj_rest_auth/jwt_auth.py:70
msgid "WIll override cookie."
msgstr "Remplacera le cookie."

#: dj_rest_auth/jwt_auth.py:81
msgid "No valid refresh token found."
msgstr "Aucun jeton d'actualisation valide n'a été trouvé."

#: dj_rest_auth/registration/serializers.py:77
msgid "Define callback_url in view"
msgstr "Définissez “callback_url” dans la vue"

#: dj_rest_auth/registration/serializers.py:86
msgid "View is not defined, pass it as a context variable"
msgstr "La “View” n’est pas définie, passez la en variable contextuelle"

#: dj_rest_auth/registration/serializers.py:91
msgid "Define adapter_class in view"
msgstr "Définissez “adapter_class” dans la vue"

#: dj_rest_auth/registration/serializers.py:117
msgid "Define client_class in view"
msgstr "Définissez “client_class” dans la vue"

#: dj_rest_auth/registration/serializers.py:138
msgid "Failed to exchange code for access token"
msgstr "Échec de l'échange de code pour le jeton d'accès"

#: dj_rest_auth/registration/serializers.py:149
msgid "Incorrect input. access_token or code is required."
msgstr "Paramètres incorrects. Il faut “access_token” ou “code”."

#: dj_rest_auth/registration/serializers.py:162
msgid "Incorrect value"
msgstr "Paramètre incorrect"

#: dj_rest_auth/registration/serializers.py:179
msgid "User is already registered with this e-mail address."
msgstr "Un utilisateur existe déjà avec cette adresse email."

#: dj_rest_auth/registration/serializers.py:237
msgid "A user is already registered with this e-mail address."
msgstr "Un utilisateur existe déjà avec cette adresse email."

#: dj_rest_auth/registration/serializers.py:246
msgid "The two password fields didn't match."
msgstr "Les deux mots de passes ne sont pas les mêmes."

#: dj_rest_auth/registration/views.py:47
msgid "Verification e-mail sent."
msgstr "Un email de vérification a été envoyé."

#: dj_rest_auth/registration/views.py:114
#: dj_rest_auth/registration/views.py:130
msgid "ok"
msgstr "ok"

#: dj_rest_auth/serializers.py:33
msgid "Must include \"email\" and \"password\"."
msgstr "Doit inclure “email” et “password”."

#: dj_rest_auth/serializers.py:42
msgid "Must include \"username\" and \"password\"."
msgstr "Doit inclure “username” et “password”."

#: dj_rest_auth/serializers.py:53
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr "Doit inclure un “username” ou “email”, et un “password”."

#: dj_rest_auth/serializers.py:99 dj_rest_auth/serializers.py:125
msgid "Unable to log in with provided credentials."
msgstr "Connexion impossible avec les informations fournies."

#: dj_rest_auth/serializers.py:106
msgid "User account is disabled."
msgstr "Le compte utilisateur est désactivé."

#: dj_rest_auth/serializers.py:116
msgid "E-mail is not verified."
msgstr "L’adresse email n’a pas été vérifiée."

#: dj_rest_auth/serializers.py:288 dj_rest_auth/serializers.py:291
msgid "Invalid value"
msgstr "Valeur incorrect"

#: dj_rest_auth/serializers.py:335
msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""
"Votre ancien mot de passe a été mal saisi. Veuillez le saisir à nouveau."

#: dj_rest_auth/views.py:162
msgid "Successfully logged out."
msgstr "Déconnexion effectuée avec succès."

#: dj_rest_auth/views.py:184
msgid "Refresh token was not included in request data."
msgstr ""
"Le jeton d'actualisation n'était pas inclus dans les données de la requête."

#: dj_rest_auth/views.py:192 dj_rest_auth/views.py:196
msgid "An error has occurred."
msgstr "Une erreur est survenue."

#: dj_rest_auth/views.py:201
msgid ""
"Neither cookies or blacklist are enabled, so the token has not been deleted "
"server side. Please make sure the token is deleted client side."
msgstr ""
"Ni les cookies ni la liste noire ne sont activés, donc le jeton n'a pas été "
"supprimé côté serveur. Veuillez vous assurer que le jeton est supprimé côté "
"client."

#: dj_rest_auth/views.py:253
msgid "Password reset e-mail has been sent."
msgstr "L’email de réinitialisation du mot de passe a été envoyé."

#: dj_rest_auth/views.py:280
msgid "Password has been reset with the new password."
msgstr "Le mot de passe a été réinitialisé."

#: dj_rest_auth/views.py:303
msgid "New password has been saved."
msgstr "Le nouveau mot de passe est sauvé."

#~ msgid "Error"
#~ msgstr "Fehler"
