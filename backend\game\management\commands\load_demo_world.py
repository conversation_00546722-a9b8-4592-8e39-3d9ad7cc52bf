# game/management/commands/load_demo_world.py
"""Populate the DB with a minimal playable world.

Usage:
    docker compose exec backend python manage.py load_demo_world

Creates:
* 6 attributes  (STR, DEX, TEC, PER, WIL, CHA)
* 6 sub‑skills  (melee, firearms, hacking, stealth, persuasion, observation)
* 2 perks       (quick_reflexes, street_smart)
* 2 scenes      (street → alley)
* 1 object      (Pi<PERSON>let w szufladzie) in scene "street"
* 3 actions     (shoot_pistol, melee_attack, hack_terminal)
* 1 transition  street → alley (always true)

Idempotent – you can run it many times; existing rows are left untouched.
"""

from django.core.management.base import BaseCommand
from django.db import transaction

from game.models import (
    Attribute, Subskill, Perk,
    Scene, GameObject, Transition,
    ActionMeta,
)


class Command(BaseCommand):
    help = "Load a tiny demo world for development / CI"

    @transaction.atomic
    def handle(self, *args, **options):
        # ───────────────── Attributes ─────────────────
        attrs = {
            "STR": "<PERSON><PERSON>",
            "DEX": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "TEC": "Technika",
            "PER": "Per<PERSON><PERSON><PERSON><PERSON>",
            "WIL": "Wola",
            "CHA": "Charyzma",
        }
        for code, name in attrs.items():
            Attribute.objects.get_or_create(code=code, defaults={"name": name})

        # ───────────────── Sub‑skills (20 skills) ────────────────
        subs = {
            # STR skills
            "melee": ("Walka wręcz", "STR"),
            "athletics": ("Atletyka", "STR"),
            "intimidation": ("Zastraszanie", "STR"),
            "demolitions": ("Materiały wybuchowe", "STR"),

            # DEX skills
            "firearms": ("Broń palna", "DEX"),
            "stealth": ("Skradanie", "DEX"),
            "lockpicking": ("Włamywanie", "DEX"),
            "driving": ("Prowadzenie", "DEX"),

            # TEC skills
            "hacking": ("Hacking", "TEC"),
            "electronics": ("Elektronika", "TEC"),
            "crafting": ("Rzemiosło", "TEC"),
            "repair": ("Naprawa", "TEC"),

            # PER skills
            "observation": ("Obserwacja", "PER"),
            "investigation": ("Śledztwo", "PER"),
            "tracking": ("Tropienie", "PER"),

            # WIL skills
            "concentration": ("Koncentracja", "WIL"),
            "resistance": ("Odporność", "WIL"),

            # CHA skills
            "persuasion": ("Perswazja", "CHA"),
            "deception": ("Oszustwo", "CHA"),
            "leadership": ("Przywództwo", "CHA"),
        }
        for code, (name, attr) in subs.items():
            Subskill.objects.get_or_create(
                code=code,
                defaults={"name": name, "primary_attr": Attribute.objects.get(code=attr)},
            )

        # ───────────────── Perks ─────────────────────
        Perk.objects.get_or_create(
            id="quick_reflexes",
            defaults=dict(
                name="Szybki refleks",
                description="+10% do uników przy akcjach DEX.",
                level_min=1,
                level_max=2,
                tags=["dex", "evasion"],
            ),
        )
        Perk.objects.get_or_create(
            id="street_smart",
            defaults=dict(
                name="Spryt uliczny",
                description="+5% do obserwacji i perswazji w mieście.",
                level_min=1,
                level_max=3,
                tags=["per", "cha"],
            ),
        )

        # ───────────────── Scenes & objects ──────────
        street, _ = Scene.objects.get_or_create(
            slug="street",
            defaults={
                "title": "Ulica",
                "mood": "neutral",
                "description": "Spokojna ulica w centrum miasta. Słychać odgłosy ruchu w oddali."
            }
        )
        alley, _ = Scene.objects.get_or_create(
            slug="alley",
            defaults={
                "title": "Zaułek",
                "mood": "mysterious",
                "description": "Ciemny zaułek między budynkami. Coś się tu czai w cieniu..."
            }
        )

        # Dodatkowe sceny demo z różnymi nastrojami
        park, _ = Scene.objects.get_or_create(
            slug="park",
            defaults={
                "title": "Park",
                "mood": "peaceful",
                "description": "Zielony park pełen drzew. Ptaki śpiewają, a fontanna szemrze spokojnie."
            }
        )

        bar, _ = Scene.objects.get_or_create(
            slug="bar",
            defaults={
                "title": "Bar",
                "mood": "energetic",
                "description": "Głośny bar pełen ludzi. Muzyka gra, a atmosfera jest elektryzująca."
            }
        )

        cemetery, _ = Scene.objects.get_or_create(
            slug="cemetery",
            defaults={
                "title": "Cmentarz",
                "mood": "melancholic",
                "description": "Stary cmentarz otoczony mgłą. Nagrobki stoją w milczeniu..."
            }
        )

        casino, _ = Scene.objects.get_or_create(
            slug="casino",
            defaults={
                "title": "Kasyno",
                "mood": "dangerous",
                "description": "Luksusowe kasyno pełne hazardu. Stawki są wysokie, a napięcie rośnie."
            }
        )

        # ───────────────── Test scenes for zombie forest loop ──────────
        zombie_forest, _ = Scene.objects.get_or_create(
            slug="zombie-forest",
            defaults={
                "title": "Las z zombie",
                "mood": "tense",
                "description": "Wchodzisz do ciemnego lasu. Słyszysz dziwne odgłosy z krzaków. Nagle z cienia wyłania się zombie, jego oczy świecą czerwienią. Co robisz?",
                "available_actions": ["zombie_fight", "zombie_run", "zombie_sneak", "zombie_talk"]
            }
        )

        safe_clearing, _ = Scene.objects.get_or_create(
            slug="safe-clearing",
            defaults={
                "title": "Bezpieczna polana",
                "mood": "peaceful",
                "description": "Udało ci się! Dotarłeś do spokojnej polany w sercu lasu. Słońce przebija się przez liście, a ptaki śpiewają. Zombie zostało daleko za tobą.",
                "available_actions": ["return_to_forest"]
            }
        )

        zombie_chase, _ = Scene.objects.get_or_create(
            slug="zombie-chase",
            defaults={
                "title": "Zombie cię dogoniło",
                "mood": "dangerous",
                "description": "Nie udało się! Zombie dogoniło cię i teraz jesteś w poważnych tarapatach. Jego zgniłe ręce sięgają w twoją stronę. Musisz szybko coś zrobić!",
                "available_actions": ["escape_to_forest"]
            }
        )

        GameObject.objects.get_or_create(
            scene=street,
            name="Pistolet w szufladzie",
            type="item",
            defaults={"metadata": {"damage": 4}},
        )

        Transition.objects.get_or_create(
            origin=street,
            target=alley,
            defaults={"condition_expr": "True"},
        )

        # Przejścia między scenami
        Transition.objects.update_or_create(
            origin=street,
            target=alley,
            defaults={
                "description": "Przejdź do tajemniczego zaułka",
                "condition_expr": "",
                "auto": False,
            },
        )

        Transition.objects.update_or_create(
            origin=street,
            target=park,
            defaults={
                "description": "Idź do spokojnego parku",
                "condition_expr": "",
                "auto": False,
            },
        )

        Transition.objects.update_or_create(
            origin=street,
            target=bar,
            defaults={
                "description": "Wejdź do energicznego baru",
                "condition_expr": "",
                "auto": False,
            },
        )

        Transition.objects.update_or_create(
            origin=alley,
            target=cemetery,
            defaults={
                "description": "Przejdź na melancholijny cmentarz",
                "condition_expr": "",
                "auto": False,
            },
        )

        Transition.objects.update_or_create(
            origin=bar,
            target=casino,
            defaults={
                "description": "Idź do niebezpiecznego kasyna",
                "condition_expr": "",
                "auto": False,
            },
        )

        # Powroty do ulicy
        for scene in [alley, park, bar, cemetery, casino]:
            Transition.objects.update_or_create(
                origin=scene,
                target=street,
                defaults={
                    "description": "Wróć na ulicę",
                    "condition_expr": "",
                    "auto": False,
                },
            )

        # ───────────────── Zombie forest loop transitions ──────────
        # From safe clearing back to forest
        Transition.objects.update_or_create(
            origin=safe_clearing,
            target=zombie_forest,
            defaults={
                "description": "Wróć do lasu zombie",
                "condition_expr": "",
                "auto": False,
            },
        )

        # From zombie chase back to forest
        Transition.objects.update_or_create(
            origin=zombie_chase,
            target=zombie_forest,
            defaults={
                "description": "Ucieknij z powrotem do lasu",
                "condition_expr": "",
                "auto": False,
            },
        )

        # From street to zombie forest (entry point)
        Transition.objects.update_or_create(
            origin=street,
            target=zombie_forest,
            defaults={
                "description": "Wejdź do lasu z zombie",
                "condition_expr": "",
                "auto": False,
            },
        )

        # ───────────────── Actions ───────────────────
        ActionMeta.objects.get_or_create(
            id="shoot_pistol",
            defaults=dict(
                label="Oddaj strzał z pistoletu",
                description="Szybki strzał w walce miejskiej.",
                base_diff=2,
                attribute=Attribute.objects.get(code="DEX"),
                subskill=Subskill.objects.get(code="firearms"),
                perk_tags=["dex", "firearms"],
                attr_growth={},  # No longer used - using percentage system
                skill_growth={},  # No automatic skill growth
            ),
        )
        ActionMeta.objects.get_or_create(
            id="melee_attack",
            defaults=dict(
                label="Atak wręcz",
                description="Rzucasz się na przeciwnika.",
                base_diff=2,
                attribute=Attribute.objects.get(code="STR"),
                subskill=Subskill.objects.get(code="melee"),
                perk_tags=["str", "melee"],
                attr_growth={},  # No longer used - using percentage system
                skill_growth={},  # No automatic skill growth
            ),
        )
        ActionMeta.objects.get_or_create(
            id="hack_terminal",
            defaults=dict(
                label="Zhakuj terminal",
                description="Włamujesz się do systemu AI.",
                base_diff=3,
                attribute=Attribute.objects.get(code="TEC"),
                subskill=Subskill.objects.get(code="hacking"),
                perk_tags=["tec", "hacking"],
                attr_growth={},  # No longer used - using percentage system
                skill_growth={},  # No automatic skill growth
            ),
        )

        # Additional actions for testing different skills
        ActionMeta.objects.get_or_create(
            id="sneak_around",
            defaults=dict(
                label="Skradaj się",
                description="Przemknij się niezauważony.",
                base_diff=2,
                attribute=Attribute.objects.get(code="DEX"),
                subskill=Subskill.objects.get(code="stealth"),
                perk_tags=["dex", "stealth"],
                attr_growth={},
                skill_growth={},
            ),
        )
        ActionMeta.objects.get_or_create(
            id="persuade_guard",
            defaults=dict(
                label="Przekonaj strażnika",
                description="Spróbuj przekonać strażnika do współpracy.",
                base_diff=3,
                attribute=Attribute.objects.get(code="CHA"),
                subskill=Subskill.objects.get(code="persuasion"),
                perk_tags=["cha", "persuasion"],
                attr_growth={},
                skill_growth={},
            ),
        )
        ActionMeta.objects.get_or_create(
            id="observe_area",
            defaults=dict(
                label="Obserwuj okolicę",
                description="Rozejrzyj się uważnie w poszukiwaniu wskazówek.",
                base_diff=2,
                attribute=Attribute.objects.get(code="PER"),
                subskill=Subskill.objects.get(code="observation"),
                perk_tags=["per", "observation"],
                attr_growth={},
                skill_growth={},
            ),
        )
        ActionMeta.objects.get_or_create(
            id="concentrate_focus",
            defaults=dict(
                label="Skoncentruj się",
                description="Zbierz myśli i skoncentruj się na zadaniu.",
                base_diff=2,
                attribute=Attribute.objects.get(code="WIL"),
                subskill=Subskill.objects.get(code="concentration"),
                perk_tags=["wil", "concentration"],
                attr_growth={},
                skill_growth={},
            ),
        )

        # ───────────────── Zombie forest actions ──────────
        ActionMeta.objects.update_or_create(
            id="zombie_fight",
            defaults=dict(
                label="Walcz z zombie",
                description="Rzuć się na zombie i spróbuj je pokonać w walce wręcz.",
                base_diff=3,
                attribute=Attribute.objects.get(code="STR"),
                subskill=Subskill.objects.get(code="melee"),
                perk_tags=["str", "melee"],
                attr_growth={},
                skill_growth={},
                action_type="skill_test",
                success_scene="safe-clearing",
                failure_scene="zombie-chase",
            ),
        )

        ActionMeta.objects.update_or_create(
            id="zombie_run",
            defaults=dict(
                label="Uciekaj przed zombie",
                description="Spróbuj uciec przed zombie używając swojej szybkości.",
                base_diff=2,
                attribute=Attribute.objects.get(code="DEX"),
                subskill=Subskill.objects.get(code="athletics"),
                perk_tags=["dex", "athletics"],
                attr_growth={},
                skill_growth={},
                action_type="skill_test",
                success_scene="safe-clearing",
                failure_scene="zombie-chase",
            ),
        )

        ActionMeta.objects.update_or_create(
            id="zombie_sneak",
            defaults=dict(
                label="Skradaj się obok zombie",
                description="Spróbuj przemknąć się obok zombie niezauważony.",
                base_diff=2,
                attribute=Attribute.objects.get(code="DEX"),
                subskill=Subskill.objects.get(code="stealth"),
                perk_tags=["dex", "stealth"],
                attr_growth={},
                skill_growth={},
                action_type="skill_test",
                success_scene="safe-clearing",
                failure_scene="zombie-chase",
            ),
        )

        ActionMeta.objects.update_or_create(
            id="zombie_talk",
            defaults=dict(
                label="Spróbuj porozmawiać z zombie",
                description="Może zombie zachowało resztki człowieczeństwa? Spróbuj z nim porozmawiać.",
                base_diff=4,
                attribute=Attribute.objects.get(code="CHA"),
                subskill=Subskill.objects.get(code="persuasion"),
                perk_tags=["cha", "persuasion"],
                attr_growth={},
                skill_growth={},
                action_type="skill_test",
                success_scene="safe-clearing",
                failure_scene="zombie-chase",
            ),
        )

        # Return actions for other scenes
        ActionMeta.objects.get_or_create(
            id="return_to_forest",
            defaults=dict(
                label="Wróć do lasu",
                description="Wróć do lasu z zombie, aby przetestować swoje umiejętności ponownie.",
                base_diff=None,
                attribute=None,
                subskill=None,
                perk_tags=[],
                attr_growth={},
                skill_growth={},
                action_type="scene_transition",
                target_scene="zombie-forest",
            ),
        )

        ActionMeta.objects.get_or_create(
            id="escape_to_forest",
            defaults=dict(
                label="Ucieknij do lasu",
                description="Ucieknij z powrotem do lasu, aby spróbować ponownie.",
                base_diff=None,
                attribute=None,
                subskill=None,
                perk_tags=[],
                attr_growth={},
                skill_growth={},
                action_type="scene_transition",
                target_scene="zombie-forest",
            ),
        )

        self.stdout.write(self.style.SUCCESS("✔ Demo world loaded / updated"))
