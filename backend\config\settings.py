"""
Django settings for config project.
"""

from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from dotenv import load_dotenv
import os

# ──────────────────────────────── Env / Paths ───────────────────────────────
load_dotenv()
BASE_DIR = Path(__file__).resolve().parent.parent

# ─────────────────────────────── Core ───────────────────────────────────────
SECRET_KEY = os.getenv("DJANGO_SECRET_KEY", "django-insecure-default-key")
DEBUG = os.getenv("DJANGO_DEBUG", "False") == "True"          # domyślnie OFF

ALLOWED_HOSTS = [h for h in os.getenv("DJANGO_ALLOWED_HOSTS", "").split(",") if h]

# ─────────────────────────────── Apps ───────────────────────────────────────
INSTALLED_APPS = [
    # Django
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sites",

    # 3rd-party
    "corsheaders",
    "rest_framework",
    "rest_framework_simplejwt.token_blacklist",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.google",
    "rest_framework.authtoken",
    "dj_rest_auth",
    "dj_rest_auth.registration",

    # Local
    "accounts",
    "game",
]

# ─────────────────────────────── Middleware ────────────────────────────────
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "allauth.account.middleware.AccountMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

# ─────────────────────────────── CORS ───────────────────────────────────────
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
]

# ─────────────────────────────── URLs / WSGI ───────────────────────────────
ROOT_URLCONF = "config.urls"
WSGI_APPLICATION = "config.wsgi.application"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    }
]

# ─────────────────────────────── Database ───────────────────────────────────
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME":     os.getenv("POSTGRES_DB",       "rpg"),
        "USER":     os.getenv("POSTGRES_USER",     "rpg_user"),
        "PASSWORD": os.getenv("POSTGRES_PASSWORD", "rpg_pass"),
        "HOST":     os.getenv("POSTGRES_HOST",     "localhost"),
        "PORT":     os.getenv("POSTGRES_PORT",     "5432"),
    }
}

# ─────────────────────────────── Custom user / Allauth ─────────────────────
AUTH_USER_MODEL = "accounts.User"
AUTHENTICATION_BACKENDS = [
    'accounts.backends.EmailVerificationBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
]
try:
    SITE_ID = int(os.getenv("DJANGO_SITE_ID", "1"))
except ValueError:
    SITE_ID = 1

# Allauth settings
ACCOUNT_AUTHENTICATION_METHOD = "email"
ACCOUNT_USER_MODEL_USERNAME_FIELD = "username"
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_EMAIL_VERIFICATION = "mandatory"
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = True
ACCOUNT_CONFIRM_EMAIL_ON_GET = True
ACCOUNT_LOGIN_ON_EMAIL_CONFIRMATION = True
ACCOUNT_DEFAULT_HTTP_PROTOCOL = "http"

SOCIALACCOUNT_PROVIDERS = {
    "google": {
        "APP": {
            "client_id": os.getenv("GOOGLE_CLIENT_ID"),
            "secret":    os.getenv("GOOGLE_CLIENT_SECRET"),
            "key": "",
        },
        "SCOPE": ["profile", "email"],
        "AUTH_PARAMS": {"access_type": "online"},
    }
}
SOCIALACCOUNT_QUERY_EMAIL        = os.getenv("SOCIALACCOUNT_QUERY_EMAIL", "True") == "True"
SOCIALACCOUNT_EMAIL_VERIFICATION = os.getenv("SOCIALACCOUNT_EMAIL_VERIFICATION", "none")
SOCIALACCOUNT_AUTO_SIGNUP        = True

ACCOUNT_ADAPTER         = "accounts.adapters.EmailVerificationAccountAdapter"
SOCIALACCOUNT_ADAPTER   = "allauth.socialaccount.adapter.DefaultSocialAccountAdapter"

# ─────────────────────────────── REST / JWT ────────────────────────────────
REST_USE_JWT = True

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME":  timedelta(minutes=int(os.getenv("JWT_ACCESS_TOKEN_LIFETIME", 30))),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=int(os.getenv("JWT_REFRESH_TOKEN_LIFETIME", 7))),
    "ROTATE_REFRESH_TOKENS":  os.getenv("JWT_ROTATE_REFRESH_TOKENS", "True") == "True",
    "BLACKLIST_AFTER_ROTATION": os.getenv("JWT_BLACKLIST_AFTER_ROTATION", "True") == "True",
}

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.TokenAuthentication",  # dla dj-rest-auth
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": int(os.getenv("DJANGO_PAGE_SIZE", 20)),
}

REST_AUTH_SERIALIZERS = {
    "LOGIN_SERIALIZER":        "dj_rest_auth.serializers.LoginSerializer",
    "TOKEN_SERIALIZER":        "dj_rest_auth.serializers.JWTSerializer",
    "SOCIAL_LOGIN_SERIALIZER": "game.api.serializers.SafeSocialLoginSerializer",
}
REST_AUTH_REGISTER_SERIALIZERS = {
    "REGISTER_SERIALIZER": "accounts.serializers.EmailRegisterSerializer",
}

# ─────────────────────────────── Logging ────────────────────────────────────
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            'format': '%(levelname)s %(asctime)s %(module)s %(message)s',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/game.log',
            'formatter': 'json',
        },
    },
    'loggers': {
        'game': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
}

# ─────────────────────────────── Custom Exception Handler ──────────────────
REST_FRAMEWORK['EXCEPTION_HANDLER'] = 'game.utils.exceptions.custom_exception_handler'

# ─────────────────────────────── Password rules ────────────────────────────
AUTH_PASSWORD_VALIDATORS = [
    {"NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"},
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

# ─────────────────────────────── i18n / tz  ────────────────────────────────
LANGUAGE_CODE = os.getenv("DJANGO_LANGUAGE_CODE", "en-us")
TIME_ZONE      = os.getenv("DJANGO_TIME_ZONE", "Europe/Warsaw")
USE_I18N = True
USE_TZ   = True

# ─────────────────────────────── Static / Media ─────────────────────────────
STATIC_URL            = "/static/"
STATIC_ROOT           = BASE_DIR / "staticfiles"
STATICFILES_STORAGE   = "whitenoise.storage.CompressedManifestStaticFilesStorage"

MEDIA_URL             = "/media/"
MEDIA_ROOT            = BASE_DIR / "media"

# ─────────────────────────────── Email ─────────────────────────────────────
EMAIL_BACKEND = os.getenv("DJANGO_EMAIL_BACKEND", "django.core.mail.backends.smtp.EmailBackend")

# SMTP Configuration
EMAIL_HOST = os.getenv("EMAIL_HOST", "smtp.gmail.com")
EMAIL_PORT = int(os.getenv("EMAIL_PORT", "587"))
EMAIL_USE_TLS = os.getenv("EMAIL_USE_TLS", "True").lower() == "true"
EMAIL_USE_SSL = os.getenv("EMAIL_USE_SSL", "False").lower() == "true"
EMAIL_HOST_USER = os.getenv("EMAIL_HOST_USER", "")
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_HOST_PASSWORD", "")

# Email addresses
DEFAULT_FROM_EMAIL = os.getenv("DJANGO_DEFAULT_FROM_EMAIL", "RPG Game <<EMAIL>>")
SERVER_EMAIL = os.getenv("DJANGO_SERVER_EMAIL", "RPG Game <<EMAIL>>")

# Email verification settings
EMAIL_SUBJECT_PREFIX = "[RPG Game] "
ACCOUNT_EMAIL_SUBJECT_PREFIX = EMAIL_SUBJECT_PREFIX

# Fallback to console backend if no SMTP credentials provided
if not EMAIL_HOST_USER or not EMAIL_HOST_PASSWORD:
    EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# ─────────────────────────────── Security tweaks ───────────────────────────
if not DEBUG:
    SECURE_SSL_REDIRECT        = True
    SESSION_COOKIE_SECURE      = True
    CSRF_COOKIE_SECURE         = True
    SECURE_HSTS_SECONDS        = 31_536_000  # 1 rok
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD        = True

# ─────────────────────────────── Misc ───────────────────────────────────────
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
