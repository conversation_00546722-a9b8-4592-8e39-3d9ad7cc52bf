# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-28 11:41+0800\n"
"PO-Revision-Date: 2018-10-28 11:45+0806\n"
"Last-Translator: b'  <<EMAIL>>'\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Translated-Using: django-rosetta 0.9.0\n"

#: registration/serializers.py:67
msgid "View is not defined, pass it as a context variable"
msgstr "View未定义，请通过context变量传入"

#: registration/serializers.py:72
msgid "Define adapter_class in view"
msgstr "请在View中定义adapter_class"

#: registration/serializers.py:91
msgid "Define callback_url in view"
msgstr "请在view中定义callback_url"

#: registration/serializers.py:95
msgid "Define client_class in view"
msgstr "请在view中定义client_class"

#: registration/serializers.py:116
msgid "Incorrect input. access_token or code is required."
msgstr "输入错误。access_token或code是必填项。"

#: registration/serializers.py:125
msgid "Incorrect value"
msgstr "错误的值"

#: registration/serializers.py:139
msgid "User is already registered with this e-mail address."
msgstr "该邮箱地址已被注册。"

#: registration/serializers.py:185
msgid "A user is already registered with this e-mail address."
msgstr "该邮箱地址已被注册。"

#: registration/serializers.py:193
msgid "The two password fields didn't match."
msgstr "两次输入的密码不相同"

#: registration/views.py:51
msgid "Verification e-mail sent."
msgstr "验证邮件已发送。"

#: registration/views.py:98
msgid "ok"
msgstr "好的"

#: serializers.py:33
msgid "Must include \"email\" and \"password\"."
msgstr "比如包含\"email\"和\"password\"。"

#: serializers.py:44
msgid "Must include \"username\" and \"password\"."
msgstr "比如包含\"username\"和\"password\"。"

#: serializers.py:57
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr "比如包含\"username\"，\"email\"，\"password\"其中一个。"

#: serializers.py:98
msgid "User account is disabled."
msgstr "用户账号已被禁用。"

#: serializers.py:101
msgid "Unable to log in with provided credentials."
msgstr "无法使用提供的信息登录。"

#: serializers.py:110
msgid "E-mail is not verified."
msgstr "邮箱未验证。"

#: views.py:127
msgid "Successfully logged out."
msgstr "已成功登出。"

#: views.py:175
msgid "Password reset e-mail has been sent."
msgstr "密码重置邮件已发送。"

#: views.py:201
msgid "Password has been reset with the new password."
msgstr "密码重置成功。"

#: views.py:223
msgid "New password has been saved."
msgstr "新密码已设置成功。"
