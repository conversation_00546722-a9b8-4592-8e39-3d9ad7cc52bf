# Generated by Django 5.2.1 on 2025-05-21 10:42

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Attribute',
            fields=[
                ('code', models.CharField(max_length=8, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=60)),
            ],
            options={
                'verbose_name': 'attribute',
                'verbose_name_plural': 'attributes',
            },
        ),
        migrations.CreateModel(
            name='Perk',
            fields=[
                ('id', models.CharField(max_length=64, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=80)),
                ('description', models.TextField()),
                ('level_min', models.PositiveSmallIntegerField()),
                ('level_max', models.PositiveSmallIntegerField()),
                ('tags', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=32), blank=True, default=list, size=None)),
            ],
            options={
                'verbose_name': 'perk',
                'verbose_name_plural': 'perks',
            },
        ),
        migrations.CreateModel(
            name='Scene',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('slug', models.SlugField(unique=True)),
                ('title', models.CharField(max_length=120)),
                ('description', models.TextField(blank=True)),
                ('diagram_json', models.JSONField(blank=True, default=dict)),
                ('entry_conditions', models.JSONField(blank=True, default=dict)),
                ('exit_effects', models.JSONField(blank=True, default=dict)),
            ],
        ),
        migrations.CreateModel(
            name='ActionMeta',
            fields=[
                ('id', models.CharField(max_length=64, primary_key=True, serialize=False)),
                ('label', models.CharField(max_length=120)),
                ('description', models.TextField(blank=True)),
                ('base_diff', models.PositiveSmallIntegerField()),
                ('perk_tags', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=32), blank=True, default=list, size=None)),
                ('attr_growth', models.JSONField(blank=True, default=dict)),
                ('skill_growth', models.JSONField(blank=True, default=dict)),
                ('attribute', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='game.attribute')),
            ],
        ),
        migrations.CreateModel(
            name='GameObject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('item', 'Item'), ('npc', 'NPC'), ('spot', 'Hot‑spot')], max_length=4)),
                ('name', models.CharField(max_length=120)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('scene', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='objects', to='game.scene')),
            ],
        ),
        migrations.CreateModel(
            name='ActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('outcome', models.CharField(max_length=20)),
                ('roll_value', models.PositiveSmallIntegerField()),
                ('target_value', models.PositiveSmallIntegerField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('action', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='game.actionmeta')),
                ('scene', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='game.scene')),
            ],
            options={
                'verbose_name': 'action log entry',
                'verbose_name_plural': 'action logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='Subskill',
            fields=[
                ('code', models.CharField(max_length=32, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=60)),
                ('primary_attr', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subskills', to='game.attribute')),
            ],
            options={
                'verbose_name': 'sub‑skill',
                'verbose_name_plural': 'sub‑skills',
            },
        ),
        migrations.AddField(
            model_name='actionmeta',
            name='subskill',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='game.subskill'),
        ),
        migrations.CreateModel(
            name='Transition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('condition_expr', models.CharField(help_text="Boolean‑like expression, e.g. 'DEX > 2 && flags.door_unlocked' ", max_length=255)),
                ('origin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outgoing', to='game.scene')),
                ('target', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incoming', to='game.scene')),
            ],
            options={
                'verbose_name': 'transition',
                'verbose_name_plural': 'transitions',
                'unique_together': {('origin', 'target')},
            },
        ),
    ]
