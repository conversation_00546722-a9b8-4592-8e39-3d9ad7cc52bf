# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-05-04 09:12+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Bulgarian <https://hosted.weblate.org/projects/allauth/django-"
"allauth/bg/>\n"
"Language: bg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.12-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Този акаунт в момента е неактивен."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Не можете да премахнете основния си имейл адрес."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Този имейл адрес вече е свързан с този акаунт."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "E-mail адресът и/или паролата, които въведохте, са грешни."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Телефонният номер и/или паролата, които въведохте, са грешни."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Вече има регистриран потребител с този имейл адрес."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Моля, въведете вашата текуща парола."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Неправилен код."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Грешна парола."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Грешен или изтекъл ключ."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Невалиден вход."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Невалиден код за възстановяване на парола."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Не можете да добавяте повече от %d e-mail адреса."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Вече има регистриран потребител с този имейл адрес."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Твърде много неуспешни опити за влизане. Опитайте отново по-късно."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "Няма потребител с този e-mail адрес."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Няма потребител с този телефонен номер."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Основният ви e-mail адрес трябва да бъде потвърден."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Това потребителско име не може да бъде използвано. Моля, изберете друго."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Потребителското име и/или паролата, които въведохте, са грешни."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Моля изберете само една опция."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Новата стойност трябва да е различна от настоящата."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Използвайте парола"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Използвайте апликация за автентикация или код"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Използвайте код за сигурност"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Изберете имейл адресите като верифицирани"

#: account/apps.py:11
msgid "Accounts"
msgstr "Акаунти"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr "Въведете телефонен номер с код на държавата (+359 за България)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Телефон"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Трябва да въведете една и съща парола."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Парола"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Запомни ме"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "E-mail адрес"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Потребителско име"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Акаунт"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Потребителско име, e-mail или телефонен номер"

#: account/forms.py:156
msgid "Username or email"
msgstr "Потребителско име или e-mail"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Потребителско име или телефон"

#: account/forms.py:160
msgid "Email or phone"
msgstr "E-mail или телефонен номер"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Забрави си паролата?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-mail (отново)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Потвърждение на e-mail адрес"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-mail (опционален)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Потребителско име(по избор)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Трябва да въведете един и същ email."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Парола (отново)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Текуща парола"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Нова парола"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Нова парола (отново)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Код"

#: account/models.py:26
msgid "user"
msgstr "потребител"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "e-mail адрес"

#: account/models.py:34
msgid "verified"
msgstr "потвърден"

#: account/models.py:35
msgid "primary"
msgstr "основен"

#: account/models.py:41
msgid "email addresses"
msgstr "email адреси"

#: account/models.py:151
msgid "created"
msgstr "създадено"

#: account/models.py:152
msgid "sent"
msgstr "изпратено"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "ключ"

#: account/models.py:158
msgid "email confirmation"
msgstr "email потвърждение"

#: account/models.py:159
msgid "email confirmations"
msgstr "email потвърждения"

#: headless/apps.py:7
msgid "Headless"
msgstr "Скрит режим"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Не можете да добавите имейл адрес към акаунт, който ползва двустепенна "
"защита."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Не можете да деактивирате двустепенна защита."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Не можете да генерирате кодове за възстановяване, без да сте включили "
"двустепенна защита."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Не можете да активирате двустепенна защита, преди да сте верифицирали имейл "
"адреса си."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Основен ключ"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Резервен ключ"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Ключ №{number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Кодове за възстановяване"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP Автентикатор"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Автентикиращ код"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Без парола"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Включвайки режим без парола Ви позволява да се вписвате чрез ключ, но има "
"допълнителни изисквания например биометрични данни или защита с ПИН код."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Вече съществува акаунт с този имейл адрес. Моля, първо влезте в този акаунт "
"и тогава свържете вашия %s акаунт."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Грешен код."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Вашият акаунт няма парола."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Вашият акаунт няма потвърден e-mail адрес."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Не можете да прекъснете последната си връзка към външен акаунт."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Този акаунт вече е свързан с друг акаунт."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Социални акаунти"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "доставчик"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "Идентификатор на доставчик"

#: socialaccount/models.py:56
msgid "name"
msgstr "име"

#: socialaccount/models.py:58
msgid "client id"
msgstr "id на клиент"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "ID на приложение, или ключ на консуматор"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "таен ключ"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "таен ключ на API, клиент или консуматор"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Ключ"

#: socialaccount/models.py:81
msgid "social application"
msgstr "социално приложение"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "социални приложения"

#: socialaccount/models.py:117
msgid "uid"
msgstr "УИД"

#: socialaccount/models.py:119
msgid "last login"
msgstr "последно влизане"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "дата на регистрация"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "допълнителни данни"

#: socialaccount/models.py:125
msgid "social account"
msgstr "социален акаунт"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "социални акаунти"

#: socialaccount/models.py:160
msgid "token"
msgstr "код"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) или access token (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "таен код"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) или refresh token (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "изтича"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "код на социално приложение"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "кодове на социални приложения"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Невалидни профилни данни"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Влизане"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Откажи"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Грешен отговор при получаване на код за заявка от \"%s\". Отговорът беше: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Грешен отговор при получаване на код за достъп от \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Няма запазен код за заявка за \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Няма запазен код за достъп за \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Няма достъп до лични данни при \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Грешен отговор при получаване на код за заявка от \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Неактивен акаунт"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Този акаунт е неактивен."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Изпратихме Ви код за потвърждение на %(recipient)s. Кода изтича скоро, моля "
"въведете го скоро."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Потвърди"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Поискай ключ"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Потвърждение на достъпа"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Моля, влезте отново, за да потвърдим сигурността на акаунта Ви."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Алтернативи"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Имейл за потвърждение"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Въведете код за потвърждение от имейла"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "e-mail адрес"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Вход"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Въведете код за влизане"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Възстановяване на парола"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Въведете код за възстановяване на паролата"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Потвърждение с телефонен номер"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Въведете код за потвърждение от телефона"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mail адреси"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Следните e-mail адреси са свързани с вашия акаунт:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Потвърден"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Непотвърден"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Основен"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Направи основен"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Изпрати потвърждение отново"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Премахни"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Добяне на е-mail адрес"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Добави e-mail"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Наистина ли искате да премахнете избрания e-mail адрес?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Получавате този имейл, защото Вие или някой друг се опитва да влезе в \n"
"акаунт използващ този имейл:\n"
"\n"
"%(email)s\n"
"\n"
"Въпреки това, акаунт с този имейл адрес вече съществува. Ако сте\n"
"забравили за този акаунт, моля използвайте процедурата за възстановяване\n"
"на паролата, за да възстановите достъпа до него:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Акаунтът вече съществува"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Здравейте от %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Благодарим, че ползвате %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Получавате този имейл, защото бяха направени следните промени по Вашият "
"акаунт:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Ако не разпознавате тези промени, моля вземете мерки за сигурността веднага. "
"Първоизточникът на промените е от:\n"
"\n"
"- IP адрес: %(ip)s\n"
"- Браузър: %(user_agent)s\n"
"- Дата: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Имейлът Ви беше променен от %(from_email)s на %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Имейлът е променен"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Имейлът Ви беше потвърден."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Имейл потвърждение"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Получавате този имейл, защото потребител %(user_display)s е дал Вашият имейл "
"адрес, за да регистрира акаунт в %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Можете да откриете Вашият код за верификация по-долу. Моля, въведете го в "
"отворения прозорец на браузъра."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "За да потвърдите, моля посетете %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Моля, потвърдете вашия e-mail адрес"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "Имейлът %(deleted_email)s е премахнат от Вашият акаунт."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Премахнат имейл"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Вашият код за влизане е посочен по-долу. Моля, въведете го в отворения "
"прозорец на браузъра."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Този имейл може да бъде игнориран безопасно, ако не сте инициирали това "
"действие."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Код за влизане"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Паролата Ви е променена."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Паролата е сменена"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Вашият код за смяна на паролата е посочен по-долу. Моля, въведете го в "
"отворения прозорец на браузъра."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Код за възстановяване на паролата"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Получавате този имейл, защото Вие или някой друг е поискал смяна на парола "
"за Вашия потребителски акаунт.\n"
"Можете да го пренебрегнете, ако не сте поискали възстановяване на парола. "
"Кликнете линка по-долу за да възстановите Вашата парола."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "В случай, че сте забравили, вашето потребителско име е %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Възстановяване на парола"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Паролата Ви е сменена."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Паролата Ви е сменена."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Зададена парола"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Получавате този имейл, защото Вие или някой друг се опита да влезе в акаунт "
"с имейл адрес %(email)s. Въпреки това, нямаме запис за такъв акаунт в нашата "
"база данни."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "Ако сте Вие, можете да се впишете за акаунт използвайки линка по-долу."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Неразпознат акаунт"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Имейл адрес"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Текущ имейл"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Промяна към"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Вашият имейл адрес все още очаква потвърждение."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Прекъсване на промяна"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Промяна към"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Промяна на имейл"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Потвърждение на e-mail адрес"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Моля, потвърдете, че <a href=\"mailto:%(email)s\">%(email)s</a> е имейл "
"адрес на потребител %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Не може да се потвърди %(email)s, защото вече е потвърден от различен акаунт."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Този линк за потвърждение на имейла е изтекъл или невалиден. Моля, <a "
"href=\"%(email_url)s\">подайте нова заявка за потвърждение на e-mail</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Ако все още не сте създали акаунт, моля, %(link)sрегистрирайте "
"се%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Влизане с код за достъп"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Изпрати ми ключ за влизане"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Изход"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Сигурни ли сте, че искате да излезете?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Не можете да премахнете основния си e-mail адрес (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Потвърждение на e-mail адрес изпратено на %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Потвърдихте e-mail адрес %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Премахнат e-mail адрес %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Успешно влязохте като %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Излязохте."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Ключ за влизане беше изпратен на %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Паролата беше сменена успешно."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Паролата беше запазена успешно."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Ключ за влизане беше изпратен на %(recipient)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Потвърдили сте телефонен номер %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Основният e-mail адрес беше избран."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Смяна на парола"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Забравена парола?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Забравили сте паролата си? Въведете вашия имейл адрес по-долу и ще ви "
"изпратим имейл с инструкции за нулиране на паролата."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Възстанови паролата ми"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Моля, свържете се с нас, ако имате проблеми с възстановяването на вашата "
"парола."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Изпратихме Ви имейл. Ако не сте го получили, моля, проверете папката със "
"спам. Ако все пак не го получите в рамките на няколко минути, моля, свържете "
"се с нас."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Грешен код"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Линкът за възстановяване на парола е невалиден, може би защото вече е бил "
"използван. Моля, заявете <a href=\"%(passwd_reset_url)s\">ново "
"възстановяване на парола</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Паролата ви е сменена."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Създаване на парола"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Промяна на телефон"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Текущ телефонен номер"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Вашият телефонен номер все още очаква потвърждение."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Напишете паролата си:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Ще получите специален код за влизане без парола."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Поискай ключ"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Други опции за влизане"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Регистрация"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Регистрация"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Вече имате акаунт? Тогава, моля, %(link)sвлезте%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Регистрация с ключ за достъп"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Регистрация с ключ за достъп"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Други опции"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Регистрацията е затворена"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Съжаляваме, но в момента регистрацията е затворена."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Забележка"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "вече сте влезли като %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Предупреждение:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"В момента нямате добавен имейл адрес. Трябва да добавите имейл адрес, за да "
"можете да получавате известия, да нулирате паролата си и др."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Потвърдете вашия e-mail адрес"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Изпратихме ви имейл за потвърждение. Следвайте връзката в него, за да "
"финализирате процеса на регистрация. Ако не виждате имейла за потвърждение в "
"основната си поща, проверете папката със спам. Моля, свържете се с нас, ако "
"не получите имейла за потвърждение в рамките на няколко минути."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Тази част от сайта изисква да потвърдим, че\n"
"Вие сте този, за когото се представяте. За тази цел е необходимо да\n"
"потвърдите собствеността на Вашия имейл адрес. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Изпратихме ви имейл за\n"
"потвърждение. Моля, кликнете върху връзката в този имейл. Ако не виждате "
"имейла за потвърждение в основната си поща, проверете папката със спам. Ако "
"не го получите\n"
"в рамките на няколко минути, моля, свържете се с нас."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Забележка:</strong> можете да <a href=\"%(email_url)s\">смените "
"вашия имейл адрес</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Съобщения:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Меню:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Свързани акаунти"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Двустепенна автентикация"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Сесии"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Вашият акаунт е защитен с двустепенна автентикация. Моля, въведете Вашият "
"ключ за автентикация:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Нов сет от ключове за възстановяване на двустепенната автентикация са "
"генерирани."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Нови ключове за възстановяване са генерирани"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Апликацията за автентикация е активирана."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Автентикиращата апликация е активирана"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Автентикиращата апликация е деактивирана."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Автентикиращата апликация е деактивирана"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Нов ключ за сигурност беше добавен."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Добавен е ключ за сигурност"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Беше премахнат ключ за сигурност."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Премахнат е ключ за сигуреност"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Автентикираща апликация"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Автентикация чрез автентикираща апликация е активирана."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Автентикиращата апликация е неактивна."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Деактивирай"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Активирай"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Ключове за сигурност"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Добавихте %(count)s ключ за сигурност."
msgstr[1] "Добавихте %(count)s ключа за сигурност."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Не бяха добавени ключове за сигурност."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Управление"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Добави"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Ключове за възстановяване"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Има %(unused_count)s налични от %(total_count)s ключове за възстановяване."
msgstr[1] ""
"Има %(unused_count)s налични от %(total_count)s ключове за възстановяване."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Няма създадени ключове за възстановяване."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Изглед"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Свали"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Генерирай"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Нов сет от ключове за възстановяване бяха генерирани."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Добавен е ключ за възстановяване."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Премахнат е ключ за възстановяване."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Въведете ключ за автентикация:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"На път сте да генерирате нов сет с ключове за възстановяване за Вашият "
"акаунт."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Това действие ще направи съществуващите ключове невалидни."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Сигурни ли сте?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Неизползвани ключове"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Свалете ключовете"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Генерирайте нови ключове"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Активация на Автентикираща апликация"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"За да защитите акаунта си с двустепенна автентикация, сканирайте QR кода по-"
"долу с Вашата автентикираща апликация. След това, въведете верифициращия код "
"от апликацията в полето по-долу."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Таен код за автентикираща апликация"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Можете да запазите този таен ключ и да го използвате ако някога "
"преинсталирате автентикиращата апликация."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Деактивирайте автентикиращата апликация"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "На път сте да деактивирате апликация за автентикация. Сигурни ли сте?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr "Вярвате ли на този браузър?"

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""
"Ако решите да се доверите на този браузър, няма да бъдете помолени за код за "
"потвърждение следващия път, когато влезете."

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr "Доверие за %(period)s"

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr "Не се доверявайте"

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Добавяне на ключ за сигурност"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Премахване на ключ за сигурност"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Сигурни ли сте, че искате да премахнете ключа за сигурност?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Употреба"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Ключ за достъп"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Ключ за сигурност"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Ключът не индикира дали е ключ за достъп или не."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Неупоменат"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Добавено на %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Последно използван на %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Редактиране"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Редактирай ключ за сигурност"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Запази"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Създай ключ за достъп"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Предстои да създадете ключ за достъп за вашия акаунт. Тъй като по-късно "
"можете да добавяте допълнителни ключове, можете да използвате описателно "
"име, за да ги разграничавате."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Създай"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Тази функционалност изисква JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Неуспешно влизане чрез приложение за автентикация на трета страна"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Възникна грешка при опит за влизане чрез вашия акаунт на трета страна."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Можете да влезете в акаунта си, използвайки някой от следните акаунти на "
"трети страни:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "В момента нямате свързани акаунти на трети страни с този акаунт."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Добавяне на акаунт на трета страна"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "Акаунт от %(provider)s беше свързан с вашия акаунт."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Добавен е акаунт на трета страна"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "Акаунт от %(provider)s беше прекъснат от вашия акаунт."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Връзката с акаунт на трета страна е прекъсната"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Свържи с %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Предстои да свържете нов акаунт на трета страна от %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Влизане с %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Предстои да влезете с акаунт на %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Продължи"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Вход прекъснат"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Прекъснахте влизането в нашия сайт чрез ваш съществуващ акаунт. Ако това е "
"било грешка, моля, <a href=\"%(login_url)s\">влезте</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Акаунт на трета страна беше свързан с Вашият."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Връзката с акаунт на трета страна беше прекъсната."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"На път сте да използвате вашия %(provider_name)s акаунт за вход в\n"
"%(site_name)s. Като последна стъпка, моля, попълнете следната форма:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Или използвайте трета страна"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Излезнахте от всички останали сесии."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Започнато на"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP Адрес"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Браузър"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Последно видян на"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Текуща"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Отпиши други сесии"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Потребителски сесии"

#: usersessions/models.py:92
msgid "session key"
msgstr "сесиен ключ"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Свързани акаунти"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Паролата трябва да бъде поне {0} символа."

#, fuzzy, python-format
#~| msgid ""
#~| "You are receiving this e-mail because you or someone else has requested "
#~| "a\n"
#~| "password for your user account. However, we do not have any record of a "
#~| "user\n"
#~| "with email %(email)s in our database.\n"
#~| "\n"
#~| "This mail can be safely ignored if you did not request a password "
#~| "reset.\n"
#~| "\n"
#~| "If it was you, you can sign up for an account using the link below."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Получавате този e-mail, защото вие или някой друг е поискал парола за "
#~ "вашия потребителски акаунт.\n"
#~ "На сървъра обаче не беше намерен потребител свързван с електронния адрес "
#~ "%(email)s.\n"
#~ "\n"
#~ "Можете да пренебрегнете това писмо, ако не сте поискали възстановяване на "
#~ "парола. Кликнете линка по-долу, за да направите нов акаунт."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "Следните e-mail адреси са свързани с вашия акаунт:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Потвърждение на e-mail адрес"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Моля, влезте с някой\n"
#~ "от съществуващите ви външни акаунти. Или %(link)sсе регистрирайте</a>\n"
#~ "за %(site_name)s акаунт и влезте по-долу:"

#~ msgid "or"
#~ msgstr "или"

#~ msgid "change password"
#~ msgstr "смени паролата"

#~ msgid "OpenID Sign In"
#~ msgstr "Вход с OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Този e-mail адрес вече е свързан с друг акаунт."
