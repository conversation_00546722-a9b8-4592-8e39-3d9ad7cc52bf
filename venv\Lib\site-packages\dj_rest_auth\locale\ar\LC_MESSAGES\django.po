# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: dj-rest-auth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-28 11:41+0800\n"
"PO-Revision-Date: 2023-02-04 21:36+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"X-Generator: Poedit 3.2.2\n"

#: registration/serializers.py:67
msgid "View is not defined, pass it as a context variable"
msgstr "لم يتم تعريف دالة العرض ، قم بتمريرها كمتغير في السياق"

#: registration/serializers.py:72
msgid "Define adapter_class in view"
msgstr "عرف \"adapter_class\" في دالة العرض"

#: registration/serializers.py:91
msgid "Define callback_url in view"
msgstr "عرف \"callback_url\" في دالة العرض"

#: registration/serializers.py:95
msgid "Define client_class in view"
msgstr "حدد \"client_class\" في دالة العرض"

#: registration/serializers.py:116
msgid "Incorrect input. access_token or code is required."
msgstr "مدخل غير صحيح. رمز وصول مطلوب."

#: registration/serializers.py:125
msgid "Incorrect value"
msgstr "قيمة غير صحيحة"

#: registration/serializers.py:139
msgid "User is already registered with this e-mail address."
msgstr "يوجد مستخدم مسجل بعنوان البريد الإلكتروني هذا."

#: registration/serializers.py:185
msgid "A user is already registered with this e-mail address."
msgstr "مستخدم مسجل بعنوان البريد الإلكتروني هذا."

#: registration/serializers.py:193
msgid "The two password fields didn't match."
msgstr "لا يتطابق حقلا كلمة المرور."

#: registration/views.py:51
msgid "Verification e-mail sent."
msgstr "تم إرسال رسالة التحقق عبر البريد الإلكتروني."

#: registration/views.py:98
#, fuzzy
msgid "ok"
msgstr "تم"

#: serializers.py:33
msgid "Must include \"email\" and \"password\"."
msgstr "يجب إدخال \"البريد الإلكتروني\" و \"كلمة المرور\"."

#: serializers.py:44
msgid "Must include \"username\" and \"password\"."
msgstr "يجب إدخال \"اسم المستخدم\" و \"كلمة المرور\"."

#: serializers.py:57
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr "يجب إدخال \"اسم المستخدم\" أو \"البريد الإلكتروني\" مع \"كلمة المرور\"."

#: serializers.py:98
msgid "User account is disabled."
msgstr "حساب المستخدم معطل."

#: serializers.py:101
msgid "Unable to log in with provided credentials."
msgstr "تعذر تسجيل الدخول بالبيانات المقدمة."

#: serializers.py:110
msgid "E-mail is not verified."
msgstr "لم يتم التحقق من البريد الإلكتروني."

#: views.py:127
msgid "Successfully logged out."
msgstr "تم تسجيل الخروج."

#: views.py:175
msgid "Password reset e-mail has been sent."
msgstr "تم إرسال البريد الإلكتروني الخاص بإعادة تعيين كلمة المرور."

#: views.py:201
msgid "Password has been reset with the new password."
msgstr "تمت إعادة تعيين كلمة المرور بكلمة المرور الجديدة."

#: views.py:223
msgid "New password has been saved."
msgstr "تم حفظ كلمة المرور الجديدة."
