from django.contrib import admin
from django.urls import include, path
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from dj_rest_auth.registration.views import SocialLoginView
from allauth.socialaccount.providers.oauth2.client import OAuth2Client
from game.api.views import UserView

class GoogleLogin(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    client_class = OAuth2Client
    callback_url = "http://localhost:5173"  # Mo<PERSON><PERSON><PERSON> usun<PERSON> jeśli niepotrzebne

urlpatterns = [
    path("admin/", admin.site.urls),

    # Autoryzacja – rejestracja, logowanie, Google
    path("api/v1/auth/", include("dj_rest_auth.urls")),
    path("api/v1/auth/registration/", include("dj_rest_auth.registration.urls")),
    path("api/v1/auth/google/", GoogleLogin.as_view(), name="google-login"),
    path("api/v1/auth/user-info/", UserView.as_view(), name="auth-user-info"),

    # Konta (opcjonalne)
    path("accounts/", include("allauth.account.urls")),
    path("accounts/", include("allauth.socialaccount.urls")),

    # Reszta API
    path("api/v1/", include("game.urls")),
]
