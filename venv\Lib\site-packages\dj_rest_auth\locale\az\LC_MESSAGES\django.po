# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-26 16:16+0200\n"
"PO-Revision-Date: 2023-06-26 16:28+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: az\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.3.1\n"

#: registration/serializers.py:67
msgid "View is not defined, pass it as a context variable"
msgstr "“View” təyin edilməyib, “context” dəyi<PERSON>əni kimi <PERSON>"

#: registration/serializers.py:72
msgid "Define adapter_class in view"
msgstr "“view” içərisində “adapter_class” təyin et"

#: registration/serializers.py:91
msgid "Define callback_url in view"
msgstr "“view” içərisində  “callback_url” təyin et"

#: registration/serializers.py:95
msgid "Define client_class in view"
msgstr "“view” içərisində  “client_class” təyin et"

#: registration/serializers.py:116
msgid "Incorrect input. access_token or code is required."
msgstr "Yanlış daxiletmə. \"access_token\" və ya \"code\" tələb olunur."

#: registration/serializers.py:125
msgid "Incorrect value"
msgstr "Yalnış dəyər"

#: registration/serializers.py:185
msgid "A user is already registered with this e-mail address."
msgstr "Bu email adresi ilə istifadəçi mövcuddur."

#: registration/serializers.py:193
msgid "The two password fields didn't match."
msgstr "İki şifrə sahəsi üst-üstə düşmür."

#: registration/views.py:98
msgid "ok"
msgstr "tamam"

#: serializers.py:33
msgid "Must include \"email\" and \"password\"."
msgstr "\"email\" və \"password\" olmalıdır."

#: serializers.py:44
msgid "Must include \"username\" and \"password\"."
msgstr "“username\" və  \"password\" olmalıdır."

#: serializers.py:57
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr "Ya ”username\" yada \"email\" və \"password\" olmalıdır."

#: serializers.py:98
msgid "User account is disabled."
msgstr "İstifadəçi hesabı deaktivdir."

#: serializers.py:101
msgid "Unable to log in with provided credentials."
msgstr "Daxil edilən məlumatlarla giriş etmək mümkün olmadı."

#: serializers.py:110
msgid "E-mail is not verified."
msgstr "Email təsdiqlənməyib."

#: views.py:127
msgid "Successfully logged out."
msgstr "Uğurlu şəkildə çıxış edildi."

#: views.py:175
msgid "Password reset e-mail has been sent."
msgstr "Şifrə sıfırlama emaili göndərildi."

#: views.py:201
msgid "Password has been reset with the new password."
msgstr "Şifrə yeni şifrə ilə sıfırlandı."

#: views.py:223
msgid "New password has been saved."
msgstr "Yeni şifrə yaddaşa verildi."
