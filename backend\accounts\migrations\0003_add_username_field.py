# Generated manually to add missing username field
# This migration adds the username field that was missing from the database

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_alter_user_options_alter_user_managers_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='username',
            field=models.CharField(max_length=150, unique=True, default='temp_user'),
            preserve_default=False,
        ),
    ]
