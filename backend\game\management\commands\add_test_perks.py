"""
Management command to add test perks to a user for testing the UI.

Usage:
    python manage.py add_test_perks --email <EMAIL>
    python manage.py add_test_perks --all-users
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from game.models import SaveGame
from game.logic.xp import get_player_save_data

User = get_user_model()


class Command(BaseCommand):
    help = "Add test perks to user for UI testing"

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Email of the user to add perks to',
        )
        parser.add_argument(
            '--all-users',
            action='store_true',
            help='Add test perks to all users',
        )
        parser.add_argument(
            '--perks',
            nargs='+',
            default=['quick_reflexes', 'wrestler', 'technical_touch'],
            help='List of perk IDs to add (default: quick_reflexes wrestler technical_touch)',
        )

    def handle(self, *args, **options):
        email = options.get('email')
        all_users = options.get('all_users')
        test_perks = options.get('perks')
        
        if not email and not all_users:
            self.stdout.write(
                self.style.ERROR('Please specify either --email or --all-users')
            )
            return
        
        # Get users to update
        if all_users:
            users = User.objects.all()
        else:
            try:
                users = [User.objects.get(email=email)]
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User with email {email} not found')
                )
                return
        
        if not users:
            self.stdout.write(self.style.WARNING('No users found'))
            return
        
        # Add perks to each user
        for user in users:
            save_data = get_player_save_data(user)
            
            # Initialize chosen_perks if not exists
            if 'chosen_perks' not in save_data:
                save_data['chosen_perks'] = []
            
            # Add test perks (avoid duplicates)
            added_perks = []
            for perk in test_perks:
                if perk not in save_data['chosen_perks']:
                    save_data['chosen_perks'].append(perk)
                    added_perks.append(perk)
            
            # Save updated data
            if added_perks:
                SaveGame.save_for(user, save_data, slot=0)
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Added perks to {user.email}: {", ".join(added_perks)}'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f'User {user.email} already has all specified perks'
                    )
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'Processed {len(users)} user(s)')
        )
