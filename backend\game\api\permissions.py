# ─────────────────────────── permissions ────────────────────────────
from rest_framework import permissions

class ReadOnlyOrStaff(permissions.BasePermission):
    """Allow GET/HEAD/OPTIONS for everyone, write only for staff."""

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user and request.user.is_staff

