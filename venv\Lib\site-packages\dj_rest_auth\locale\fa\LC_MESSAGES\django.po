# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-05-11 21:48+0430\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: HOSSEIN SHAKIBA <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: registration/serializers.py:75
msgid "Define callback_url in view"
msgstr "تعریف کنید view را در callback_url"

#: registration/serializers.py:142
msgid "Incorrect input. access_token or code is required."
msgstr ".ورودی اشتباه. توکن دسترسی یا کد لازم است"

#: registration/serializers.py:151
msgid "Incorrect value"
msgstr "مقدار اشتباه"

#: registration/serializers.py:165
msgid "User is already registered with this e-mail address."
msgstr ".کاربر قبلاً با این آدرس ایمیل ثبت شده است"

#: registration/serializers.py:211
msgid "A user is already registered with this e-mail address."
msgstr ".کاربری قبلاً با این آدرس ایمیل ثبت شده است"

#: registration/serializers.py:219
msgid "The two password fields didn't match."
msgstr ".رمزهای عبور با هم مطابقت ندارند"

#: registration/views.py:48
msgid "Verification e-mail sent."
msgstr ".ایمیل تأیید ارسال شد"

#: registration/views.py:101
msgid "ok"
msgstr "باشه"

#: serializers.py:37
msgid "Must include \"email\" and \"password\"."
msgstr ".باید شامل ایمیل و پسورد باشد"

#: serializers.py:48
msgid "Must include \"username\" and \"password\"."
msgstr ".باید شامل نام کاربری و پسورد باشد"

#: serializers.py:61
msgid "Must include either \"username\" or \"email\" and \"password\"."
msgstr ".باید شامل نام کاربری یا ایمیل و پسورد باشد"

#: serializers.py:106
msgid "User account is disabled."
msgstr ".حساب کاربر غیرفعال است"

#: serializers.py:114
msgid "E-mail is not verified."
msgstr ".ایمیل تايید نشده است"

#: serializers.py:123
msgid "Unable to log in with provided credentials."
msgstr ".ورود به سیستم با اطلاعات ارائه شده امکان پذیر نیست"

#: serializers.py:319
msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ".رمز ورود قدیمی شما به اشتباه وارد شده است. لطفا دوباره وارد کنید"

#: views.py:150
msgid "Successfully logged out."
msgstr ".با موفقیت از سیستم خارج شدید"

#: views.py:171
msgid "Refresh token was not included in request data."
msgstr "."

#: views.py:179 views.py:183
msgid "An error has occurred."
msgstr ".خطایی رخ داده است"

#: views.py:240
msgid "Password reset e-mail has been sent."
msgstr ".ایمیل تنظیم مجدد رمز عبور ارسال شده است"

#: views.py:267
msgid "Password has been reset with the new password."
msgstr ".گذرواژه با گذرواژه جدید بازنشانی شد"

#: views.py:290
msgid "New password has been saved."
msgstr ".گذرواژه جدید ذخیره شده است"
