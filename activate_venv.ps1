# PowerShell script to activate virtual environment
Write-Host "Activating Python virtual environment..." -ForegroundColor Green

# Change to project directory
Set-Location "F:\Grimveil\rpg-project"

# Activate virtual environment
& .\venv\Scripts\Activate.ps1

Write-Host ""
Write-Host "Virtual environment activated!" -ForegroundColor Green
Write-Host "Python version:" -ForegroundColor Yellow
python --version
Write-Host ""
Write-Host "Pip version:" -ForegroundColor Yellow
pip --version
Write-Host ""
Write-Host "You can now run Django commands like:" -ForegroundColor Cyan
Write-Host "  python backend\manage.py runserver" -ForegroundColor White
Write-Host "  python backend\manage.py migrate" -ForegroundColor White
Write-Host "  python backend\manage.py test" -ForegroundColor White
Write-Host ""
