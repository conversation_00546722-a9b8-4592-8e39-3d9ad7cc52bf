from django.contrib import admin
from .models import (
    Attribute, Subskill, Perk, Chapter, Subchapter, Scene, GameObject,
    Transition, ActionMeta, ActionLog, SaveGame
)


@admin.register(Attribute)
class AttributeAdmin(admin.ModelAdmin):
    list_display = ('code', 'name')
    search_fields = ('code', 'name')
    ordering = ('code',)


@admin.register(Subskill)
class SubskillAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'primary_attr')
    list_filter = ('primary_attr',)
    search_fields = ('code', 'name')
    ordering = ('code',)


@admin.register(Perk)
class PerkAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'level_min', 'level_max', 'get_tags')
    list_filter = ('level_min', 'level_max', 'tags')
    search_fields = ('id', 'name', 'description')
    ordering = ('level_min', 'id')

    def get_tags(self, obj):
        return ', '.join(obj.tags) if obj.tags else '-'
    get_tags.short_description = 'Tags'

    fieldsets = (
        (None, {
            'fields': ('id', 'name', 'description')
        }),
        ('Level Requirements', {
            'fields': ('level_min', 'level_max')
        }),
        ('Tags', {
            'fields': ('tags',),
            'description': 'Tags that determine when this perk applies (e.g., dex, firearms, stealth)'
        }),
    )


@admin.register(Chapter)
class ChapterAdmin(admin.ModelAdmin):
    list_display = ('order', 'title', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('title', 'description')
    ordering = ('order',)


@admin.register(Subchapter)
class SubchapterAdmin(admin.ModelAdmin):
    list_display = ('chapter', 'order', 'title', 'is_active')
    list_filter = ('chapter', 'is_active')
    search_fields = ('title', 'description')
    ordering = ('chapter__order', 'order')


@admin.register(Scene)
class SceneAdmin(admin.ModelAdmin):
    list_display = ('slug', 'title', 'subchapter', 'mood', 'get_available_actions_count')
    list_filter = ('mood', 'subchapter__chapter', 'subchapter')
    search_fields = ('slug', 'title', 'description')
    ordering = ('subchapter__chapter__order', 'subchapter__order', 'slug')

    def get_available_actions_count(self, obj):
        return len(obj.available_actions) if obj.available_actions else 0
    get_available_actions_count.short_description = 'Actions Count'


@admin.register(ActionMeta)
class ActionMetaAdmin(admin.ModelAdmin):
    list_display = ('id', 'label', 'base_diff', 'attribute', 'subskill', 'get_perk_tags')
    list_filter = ('base_diff', 'attribute', 'subskill')
    search_fields = ('id', 'label', 'description')
    ordering = ('id',)

    def get_perk_tags(self, obj):
        return ', '.join(obj.perk_tags) if obj.perk_tags else '-'
    get_perk_tags.short_description = 'Perk Tags'


@admin.register(GameObject)
class GameObjectAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'type', 'scene')
    list_filter = ('type', 'scene')
    search_fields = ('name', 'scene__title')
    ordering = ('scene', 'name')


@admin.register(Transition)
class TransitionAdmin(admin.ModelAdmin):
    list_display = ('id', 'origin', 'target', 'auto', 'get_condition_short')
    list_filter = ('auto', 'origin', 'target')
    search_fields = ('origin', 'target', 'description')
    ordering = ('origin', 'target')

    def get_condition_short(self, obj):
        if not obj.condition_expr:
            return 'Always'
        return obj.condition_expr[:50] + '...' if len(obj.condition_expr) > 50 else obj.condition_expr
    get_condition_short.short_description = 'Condition'


@admin.register(ActionLog)
class ActionLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'action', 'scene', 'outcome', 'roll_value', 'target_value', 'timestamp')
    list_filter = ('outcome', 'scene', 'timestamp')
    search_fields = ('action__label', 'scene__title')
    ordering = ('-timestamp',)
    readonly_fields = ('timestamp',)


@admin.register(SaveGame)
class SaveGameAdmin(admin.ModelAdmin):
    list_display = ('user', 'slot', 'get_level', 'get_scene', 'updated_at')
    list_filter = ('slot', 'updated_at')
    search_fields = ('user__email',)
    ordering = ('-updated_at',)
    readonly_fields = ('updated_at',)

    def get_level(self, obj):
        return obj.data.get('level', 'N/A') if obj.data else 'N/A'
    get_level.short_description = 'Level'

    def get_scene(self, obj):
        return obj.data.get('scene', 'N/A') if obj.data else 'N/A'
    get_scene.short_description = 'Current Scene'
